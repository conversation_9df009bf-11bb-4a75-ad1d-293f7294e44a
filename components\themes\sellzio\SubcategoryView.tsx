'use client'

import React from 'react';
import { motion } from 'framer-motion';
import { ChevronLeft } from 'lucide-react';

// Interface untuk Category
interface Category {
  id: string;
  name: string;
  icon: string;
  color?: string;
  slug?: string;
  banner?: string;
  subcategories?: Category[];
}

// Interface untuk SubcategoryView
interface SubcategoryViewProps {
  category: Category;
  subcategories: Category[];
  allCategories: Category[];
  onBack: () => void;
  onSubcategoryClick: (subcategory: Category) => void;
  onCategoryChange: (category: Category) => void;
}

// Icon mapping untuk kategori utama - sesuai dengan data yang baru
const categoryIcons: { [key: string]: string } = {
  'Elektronik': '📱',
  'Komputer & Aksesoris': '💻',
  'Handphone & Aksesoris': '📱',
  'Pakaian Pria': '👕',
  'Sepatu Pria': '👟',
  'Tas Pria': '👜',
  'Aksesoris Fashion': '💍',
  'Jam Tangan': '⌚',
  'Kesehatan': '🏥',
  '<PERSON><PERSON> & Koleksi': '📷',
  'default': '🛒'
};

// Icon mapping untuk subkategori - lengkap sesuai categories.tsx
const subcategoryIcons: { [key: string]: string } = {
  // Elektronik
  'Konsol Game': '🎮', 'Aksesoris Konsol': '🎮', 'Alat Casing': '🔧', 'Foot Bath & Spa': '💊',
  'Mesin Jahit & Aksesoris': '🔧', 'Setrika & Mesin Uap': '🔥', 'Purifier & Humidifier': '❄️',
  'Perangkat Debu & Peralatan Perawatan Lantai': '🔧', 'Telepon': '📱', 'Mesin Cuci & Pengering': '🧺',
  'Water Heater': '🌡️', 'Pendingin Ruangan': '❄️', 'Pengering Sepatu': '👟', 'Penghangat Ruangan': '🌡️',
  'TV & Aksesoris': '📺', 'Perangkat Dapur': '🔥', 'Lampu': '💡', 'Kamera Keamanan': '📷',
  'Video Game': '🎮', 'Kelastrian': '🔋', 'Baterai': '🔋', 'Rokok Elektronik & Shisha': '💊',
  'Remote Kontrol': '📱', 'Walkie Talkie': '📱', 'Media Player': '📺', 'Perangkat Audio & Speaker': '🎧',
  'Elektronik Lainnya': '📱',

  // Komputer & Aksesoris
  'Desktop': '💻', 'Monitor': '💻', 'Komponen Desktop & Laptop': '📱', 'Penyimpanan Data': '📦',
  'Komponen Network': '📱', 'Software': '📱', 'Peralatan Kantor': '📱', 'Printer & Scanner': '📱',
  'Aksesoris Desktop & Laptop': '📱', 'Keyboard & Mouse': '📱', 'Laptop': '💻', 'Gaming': '🎮',
  'Audio Computer': '🎧', 'Proyektor & Aksesoris': '📱', 'Komputer & Aksesoris Lainnya': '📱',

  // Handphone & Aksesoris
  'Kartu Perdana': '📱', 'Tablet': '📱', 'Handphone': '📱', 'Perangkat Wearable': '⌚',
  'Perangkat VR': '📱', 'Aksesoris Selfie': '📷', 'Handphone & Tablet Aksesoris': '📱',
  'Kartu Memori': '📦', 'Kabel, Charger, & Konverter': '🔋', 'Powerbank & Baterai': '🔋',
  'Casing & Skin': '📱', 'Audio Handphone': '🎧', 'Handphone & Aksesoris Lainnya': '📱',

  // Pakaian Pria
  'Denim': '👕', 'Hoodie & Sweatshirt': '👕', 'Sweater & Cardigan': '👕', 'Jaket, Mantel, & Rompi': '👕',
  'Jas Formal': '👔', 'Celana Panjang': '👕', 'Celana Pendek': '👕', 'Atasan': '👕', 'Batik': '👕',
  'Pakaian Dalam': '👕', 'Pakaian Tidur': '👕', 'Set Pakaian Pria': '👕', 'Pakaian Tradisional': '👕',
  'Kostum': '👕', 'Pakaian Kerja': '👔', 'Pakaian Pria Lainnya': '👕',

  // Sepatu Pria
  'Sneakers': '👟', 'Kaos Kaki': '👟', 'Sandal': '👟', 'Aksesoris & Perawatan Sepatu': '🔧',
  'Boot': '👟', 'Tall Sepatu': '👟', 'Slip-On & Mules': '👟', 'Sepatu Pria Lainnya': '👟',
  'Sepatu Formal': '👞',

  // Tas Pria
  'Tas Selempang & Bahu Pria': '👜', 'Dompet': '👜', 'Ransel Pria': '🎒', 'Tas Pinggang Pria': '👜',
  'Tas Laptop': '💻', 'Clutch': '👜', 'Tote Bag': '👜', 'Tas Kerja': '💼', 'Tas Pria Lainnya': '👜',

  // Aksesoris Fashion
  'Cincin': '💍', 'Anting': '💍', 'Syal & Selendang': '🧣', 'Sarung Tangan': '🧤',
  'Aksesoris Rambut': '💇', 'Gelang Tangan & Bangle': '💍', 'Gelang Kaki': '💍', 'Topi': '👒',
  'Kalung': '💍', 'Kacamata & Aksesoris': '👓', 'Lensa Kontak & Aksesoris': '👓', 'Logam Mulia': '💰',
  'Ikat Pinggang': '👔', 'Dasi': '👔', 'Aksesoris Tambahan': '💍', 'Set & Paket Aksesoris': '💍',
  'Perhiasan Berharga': '💎', 'Aksesoris Fashion Lainnya': '💍',

  // Jam Tangan
  'Jam Tangan Wanita': '⌚', 'Jam Tangan Pria': '⌚', 'Jam Tangan Couple': '⌚',
  'Aksesoris Jam Tangan': '⌚', 'Jam Tangan Lainnya': '⌚',

  // Kesehatan
  'Kewanitaan': '💊', 'Kesehatan Seksual': '💊', 'Perawatan Mata': '👁️', 'Suplemen Makanan': '💊',
  'Obat-obatan & Alat Kesehatan': '💊', 'Alat Tes & Monitor': '🩺', 'P3K': '🏥',
  'Alat Bantu Cedera & Disabilitas': '🩺', 'Obat Nyamuk': '💊', 'Popok Dewasa': '💊',
  'Hand Sanitizer': '🧴', 'Minyak Esensial': '🧴', 'Perawatan Hidung & Pernapasan': '💊',
  'Perawatan Telinga': '💊', 'Perawatan Mulut': '🦷', 'Kesehatan Lainnya': '💊',

  // Hobi & Koleksi
  'Aksesoris Hewan Peliharaan': '🐕', 'Litter & Toilet': '🐕', 'Grooming Hewan': '🐕',
  'Pakaian & Aksesoris Hewan': '🐕', 'Perawatan Kesehatan Hewan': '🐕', 'Makanan Hewan': '🐕',
  'Koleksi': '📦', 'Mainan & Games': '🎲', 'CD, DVD & Bluray': '💿', 'Alat & Aksesoris Musik': '🎵',
  'Piringan Hitam': '💿', 'Album Foto': '📸', 'Perlengkapan Menjahit': '🧵', 'Hobi & Koleksi Lainnya': '⭐',

  'default': '🛒'
};

const SubcategoryView: React.FC<SubcategoryViewProps> = ({
  category,
  subcategories,
  allCategories,
  onBack,
  onSubcategoryClick,
  onCategoryChange
}) => {
  const [showCategorySelector, setShowCategorySelector] = React.useState(false);
  return (
    <div className="fixed inset-0 bg-white z-50 overflow-y-auto" style={{ paddingTop: '80px' }}>
      {/* Header */}
      <div className="sticky top-0 bg-white border-b border-gray-200 px-4 py-3 z-10">
        <div className="flex items-center">
          <button 
            onClick={onBack}
            className="flex items-center text-gray-600 hover:text-gray-800 transition-colors mr-3"
          >
            <ChevronLeft size={20} />
          </button>
          <div className="flex items-center flex-1">
            <div
              className="w-8 h-8 rounded-full flex items-center justify-center mr-3"
              style={{ backgroundColor: `${category.color}1a` }}
            >
              <span className="text-2xl">
                {categoryIcons[category.name] || categoryIcons['default']}
              </span>
            </div>
            <h1 className="text-lg font-semibold text-gray-800">{category.name}</h1>
          </div>

          {/* Tombol untuk ganti kategori */}
          <button
            onClick={() => setShowCategorySelector(true)}
            className="ml-2 px-3 py-1 bg-gray-100 rounded-full text-sm text-gray-600 hover:bg-gray-200 transition-colors"
          >
            Ganti
          </button>
        </div>
      </div>

      {/* Daftar Subkategori */}
      <div className="p-4">
        <div className="grid grid-cols-4 gap-3">
          {subcategories.map((subcategory) => (
            <motion.div
              key={subcategory.id}
              className="flex flex-col items-center p-2 rounded-lg cursor-pointer hover:bg-gray-50 transition-colors"
              whileHover={{ scale: 1.03 }}
              whileTap={{ scale: 0.97 }}
              onClick={() => onSubcategoryClick(subcategory)}
            >
              <div 
                className="w-12 h-12 rounded-full flex items-center justify-center text-2xl mb-1"
                style={{ backgroundColor: `${category.color}1a` }}
              >
                {subcategoryIcons[subcategory.name] || subcategoryIcons['default']}
              </div>
              <span className="text-xs text-center font-medium text-gray-700 line-clamp-2">
                {subcategory.name}
              </span>
            </motion.div>
          ))}
        </div>
      </div>

      {/* Modal Selector Kategori */}
      {showCategorySelector && (
        <div className="fixed inset-0 bg-black bg-opacity-50 z-60 flex items-end">
          <div className="bg-white w-full rounded-t-2xl max-h-[70vh] overflow-y-auto">
            <div className="sticky top-0 bg-white border-b border-gray-200 px-4 py-3">
              <div className="flex items-center justify-between">
                <h2 className="text-lg font-semibold text-gray-800">Pilih Kategori</h2>
                <button
                  onClick={() => setShowCategorySelector(false)}
                  className="text-gray-500 hover:text-gray-700"
                >
                  ✕
                </button>
              </div>
            </div>

            <div className="p-4">
              <div className="grid grid-cols-4 gap-3">
                {allCategories.map((cat) => (
                  <motion.div
                    key={cat.id}
                    className={`flex flex-col items-center p-2 rounded-lg cursor-pointer transition-colors ${
                      cat.id === category.id ? 'bg-blue-50 border border-blue-200' : 'hover:bg-gray-50'
                    }`}
                    whileHover={{ scale: 1.03 }}
                    whileTap={{ scale: 0.97 }}
                    onClick={() => {
                      onCategoryChange(cat);
                      setShowCategorySelector(false);
                    }}
                  >
                    <div
                      className="w-12 h-12 rounded-full flex items-center justify-center text-2xl mb-1"
                      style={{ backgroundColor: `${cat.color}1a` }}
                    >
                      <span className="text-2xl">
                        {categoryIcons[cat.name] || categoryIcons['default']}
                      </span>
                    </div>
                    <span className="text-xs text-center font-medium text-gray-700 line-clamp-2">
                      {cat.name}
                    </span>
                  </motion.div>
                ))}
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default SubcategoryView;
