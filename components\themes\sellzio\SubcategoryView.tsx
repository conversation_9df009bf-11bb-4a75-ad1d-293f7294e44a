'use client'

import React from 'react';
import { motion } from 'framer-motion';
import { ChevronLeft } from 'lucide-react';

// Interface untuk Category
interface Category {
  id: string;
  name: string;
  icon: string;
  color?: string;
  slug?: string;
  banner?: string;
  subcategories?: Category[];
}

// Interface untuk SubcategoryView
interface SubcategoryViewProps {
  category: Category;
  subcategories: Category[];
  allCategories: Category[];
  onBack: () => void;
  onSubcategoryClick: (subcategory: Category) => void;
  onCategoryChange: (category: Category) => void;
}

// Icon mapping untuk subkategori
const subcategoryIcons: { [key: string]: string } = {
  'Smartphone': '📱',
  'Laptop': '💻',
  'Tablet': '📱',
  'Headphone': '🎧',
  'Speaker': '🔊',
  'Smartwatch': '⌚',
  'Power Bank': '🔋',
  'Charger': '🔌',
  'Baju Pria': '👔',
  'Baju Wanita': '👗',
  'Celana': '👖',
  'Sepatu': '👟',
  'Tas': '👜',
  'Aksesoris': '💍',
  'Jam Tangan': '⌚',
  'Kacamata': '🕶️',
  '<PERSON><PERSON><PERSON>': '🍿',
  'Minuman': '🥤',
  'Makanan Beku': '🧊',
  'Bumbu Dapur': '🧂',
  'Buah & Sayur': '🥬',
  'Daging & Ikan': '🥩',
  'Roti & Kue': '🍞',
  'Susu & Telur': '🥛',
  'default': '📦'
};

const SubcategoryView: React.FC<SubcategoryViewProps> = ({
  category,
  subcategories,
  allCategories,
  onBack,
  onSubcategoryClick,
  onCategoryChange
}) => {
  const [showCategorySelector, setShowCategorySelector] = React.useState(false);
  return (
    <div className="fixed inset-0 bg-white z-50 overflow-y-auto" style={{ paddingTop: '60px' }}>
      {/* Header */}
      <div className="sticky top-0 bg-white border-b border-gray-200 px-4 py-3 z-10">
        <div className="flex items-center">
          <button 
            onClick={onBack}
            className="flex items-center text-gray-600 hover:text-gray-800 transition-colors mr-3"
          >
            <ChevronLeft size={20} />
          </button>
          <div className="flex items-center flex-1">
            <div
              className="w-8 h-8 rounded-full flex items-center justify-center mr-3"
              style={{ backgroundColor: `${category.color}1a` }}
            >
              <span style={{ color: category.color }}>
                {subcategoryIcons[category.name] || subcategoryIcons['default']}
              </span>
            </div>
            <h1 className="text-lg font-semibold text-gray-800">{category.name}</h1>
          </div>

          {/* Tombol untuk ganti kategori */}
          <button
            onClick={() => setShowCategorySelector(true)}
            className="ml-2 px-3 py-1 bg-gray-100 rounded-full text-sm text-gray-600 hover:bg-gray-200 transition-colors"
          >
            Ganti
          </button>
        </div>
      </div>

      {/* Daftar Subkategori */}
      <div className="p-4">
        <div className="grid grid-cols-4 gap-3">
          {subcategories.map((subcategory) => (
            <motion.div
              key={subcategory.id}
              className="flex flex-col items-center p-2 rounded-lg cursor-pointer hover:bg-gray-50 transition-colors"
              whileHover={{ scale: 1.03 }}
              whileTap={{ scale: 0.97 }}
              onClick={() => onSubcategoryClick(subcategory)}
            >
              <div 
                className="w-12 h-12 rounded-full flex items-center justify-center text-2xl mb-1"
                style={{ backgroundColor: `${category.color}1a` }}
              >
                {subcategoryIcons[subcategory.name] || subcategoryIcons['default']}
              </div>
              <span className="text-xs text-center font-medium text-gray-700 line-clamp-2">
                {subcategory.name}
              </span>
            </motion.div>
          ))}
        </div>
      </div>

      {/* Modal Selector Kategori */}
      {showCategorySelector && (
        <div className="fixed inset-0 bg-black bg-opacity-50 z-60 flex items-end">
          <div className="bg-white w-full rounded-t-2xl max-h-[70vh] overflow-y-auto">
            <div className="sticky top-0 bg-white border-b border-gray-200 px-4 py-3">
              <div className="flex items-center justify-between">
                <h2 className="text-lg font-semibold text-gray-800">Pilih Kategori</h2>
                <button
                  onClick={() => setShowCategorySelector(false)}
                  className="text-gray-500 hover:text-gray-700"
                >
                  ✕
                </button>
              </div>
            </div>

            <div className="p-4">
              <div className="grid grid-cols-4 gap-3">
                {allCategories.map((cat) => (
                  <motion.div
                    key={cat.id}
                    className={`flex flex-col items-center p-2 rounded-lg cursor-pointer transition-colors ${
                      cat.id === category.id ? 'bg-blue-50 border border-blue-200' : 'hover:bg-gray-50'
                    }`}
                    whileHover={{ scale: 1.03 }}
                    whileTap={{ scale: 0.97 }}
                    onClick={() => {
                      onCategoryChange(cat);
                      setShowCategorySelector(false);
                    }}
                  >
                    <div
                      className="w-12 h-12 rounded-full flex items-center justify-center text-2xl mb-1"
                      style={{ backgroundColor: `${cat.color}1a` }}
                    >
                      <span style={{ color: cat.color }}>
                        {subcategoryIcons[cat.name] || subcategoryIcons['default']}
                      </span>
                    </div>
                    <span className="text-xs text-center font-medium text-gray-700 line-clamp-2">
                      {cat.name}
                    </span>
                  </motion.div>
                ))}
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default SubcategoryView;
