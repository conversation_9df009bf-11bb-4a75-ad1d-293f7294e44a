'use client';

import { Suspense } from 'react';
import { useSearchParams } from 'next/navigation';
import SellzioLayout from '@/components/themes/sellzio/layout';

function SearchContent() {
  const searchParams = useSearchParams();
  const query = searchParams.get('q') || '';
  const category = searchParams.get('category') || '';

  // Mock data produk untuk demo
  const mockProducts = [
    {
      id: 1,
      name: `${query} Premium`,
      price: 'Rp 299.000',
      originalPrice: 'Rp 399.000',
      discount: '25%',
      image: '/api/placeholder/200/200',
      rating: 4.5,
      sold: 150,
      location: 'Jakarta'
    },
    {
      id: 2,
      name: `${query} Pro Max`,
      price: 'Rp 599.000',
      originalPrice: 'Rp 799.000',
      discount: '25%',
      image: '/api/placeholder/200/200',
      rating: 4.8,
      sold: 89,
      location: 'Surabaya'
    },
    {
      id: 3,
      name: `${query} Standard`,
      price: 'Rp 199.000',
      originalPrice: 'Rp 249.000',
      discount: '20%',
      image: '/api/placeholder/200/200',
      rating: 4.3,
      sold: 234,
      location: 'Bandung'
    },
    {
      id: 4,
      name: `${query} Lite`,
      price: 'Rp 149.000',
      originalPrice: 'Rp 199.000',
      discount: '25%',
      image: '/api/placeholder/200/200',
      rating: 4.1,
      sold: 67,
      location: 'Medan'
    }
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header Search Results */}
      <div className="bg-white border-b border-gray-200 py-4">
        <div className="max-w-7xl mx-auto px-4">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-xl font-semibold text-gray-800">
                Hasil pencarian untuk "{query}"
              </h1>
              {category && (
                <p className="text-sm text-gray-600 mt-1">
                  dalam kategori {category}
                </p>
              )}
            </div>
            <div className="text-sm text-gray-600">
              {mockProducts.length} produk ditemukan
            </div>
          </div>
        </div>
      </div>

      {/* Filter Bar */}
      <div className="bg-white border-b border-gray-200 py-3">
        <div className="max-w-7xl mx-auto px-4">
          <div className="flex items-center space-x-4">
            <button className="flex items-center space-x-2 px-3 py-2 border border-gray-300 rounded-lg hover:bg-gray-50">
              <span className="text-sm">Urutkan</span>
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
              </svg>
            </button>
            <button className="flex items-center space-x-2 px-3 py-2 border border-gray-300 rounded-lg hover:bg-gray-50">
              <span className="text-sm">Filter</span>
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.207A1 1 0 013 6.5V4z" />
              </svg>
            </button>
          </div>
        </div>
      </div>

      {/* Products Grid */}
      <div className="max-w-7xl mx-auto px-4 py-6">
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4">
          {mockProducts.map((product) => (
            <div key={product.id} className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden hover:shadow-md transition-shadow">
              <div className="aspect-square relative">
                <img 
                  src={product.image} 
                  alt={product.name}
                  className="w-full h-full object-cover"
                />
                {product.discount && (
                  <div className="absolute top-2 left-2 bg-red-500 text-white text-xs px-2 py-1 rounded">
                    {product.discount}
                  </div>
                )}
              </div>
              <div className="p-3">
                <h3 className="text-sm font-medium text-gray-800 line-clamp-2 mb-2">
                  {product.name}
                </h3>
                <div className="space-y-1">
                  <div className="flex items-center space-x-2">
                    <span className="text-lg font-bold text-orange-600">
                      {product.price}
                    </span>
                    {product.originalPrice && (
                      <span className="text-sm text-gray-500 line-through">
                        {product.originalPrice}
                      </span>
                    )}
                  </div>
                  <div className="flex items-center justify-between text-xs text-gray-600">
                    <div className="flex items-center space-x-1">
                      <span>⭐ {product.rating}</span>
                      <span>•</span>
                      <span>{product.sold} terjual</span>
                    </div>
                  </div>
                  <div className="text-xs text-gray-500">
                    {product.location}
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}

export default function SearchPage() {
  return (
    <SellzioLayout>
      <Suspense fallback={
        <div className="min-h-screen bg-gray-50 flex items-center justify-center">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-orange-600 mx-auto mb-4"></div>
            <p className="text-gray-600">Mencari produk...</p>
          </div>
        </div>
      }>
        <SearchContent />
      </Suspense>
    </SellzioLayout>
  );
}
