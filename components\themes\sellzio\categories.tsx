'use client'

import React, { useState, useRef, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import {
  Smartphone,
  Shirt,
  Utensils,
  ShoppingCart,
  Gamepad2,
  MapPin,
  Heart,
  Car,
  Home,
  Book,
  ChevronLeft,
  Grid3X3
} from 'lucide-react'
import SubcategoryView from './SubcategoryView'

// Interface untuk Category
interface Category {
  id: string;
  name: string;
  icon: string;
  color?: string;
  slug?: string;
  banner?: string;
  subcategories?: Category[];
}

// Interface untuk SubcategoryView
interface SubcategoryViewProps {
  category: Category;
  subcategories: Category[];
  onBack: () => void;
  onSubcategoryClick: (subcategory: Category) => void;
}

// Fungsi untuk generate ID
const generateId = (prefix: string, name: string) => {
  return `${prefix}-${name.toLowerCase().replace(/\s+/g, '-')}`
}

// Fungsi untuk mendapatkan icon
const getIcon = (iconName: string) => {
  const iconMap: { [key: string]: React.ReactNode } = {
    'device-mobile': <Smartphone size={20} />,
    'shirt': <Shirt size={20} />,
    'utensils': <Utensils size={20} />,
    'shopping-cart': <ShoppingCart size={20} />,
    'gamepad': <Gamepad2 size={20} />,
    'map-pin': <MapPin size={20} />,
    'heart': <Heart size={20} />,
    'car': <Car size={20} />,
    'home': <Home size={20} />,
    'book': <Book size={20} />
  }
  return iconMap[iconName] || <ShoppingCart size={20} />
}

// Data kategori - sama seperti Velozio
const allCategories: Category[] = [
  { id: generateId('kategori', 'elektronik'), name: "Elektronik", icon: 'device-mobile', color: "#1BA0E2" },
  { id: generateId('kategori', 'fashion'), name: "Fashion", icon: 'shirt', color: "#F1C40F" },
  { id: generateId('kategori', 'makanan'), name: "Makanan", icon: 'utensils', color: "#F25C05" },
  { id: generateId('kategori', 'belanja'), name: "Belanja", icon: 'shopping-cart', color: "#E74C3C" },
  { id: generateId('kategori', 'game'), name: "Game", icon: 'gamepad', color: "#3498DB" },
  { id: generateId('kategori', 'lokal'), name: "Lokal", icon: 'map-pin', color: "#9B59B6" },
  { id: generateId('kategori', 'kecantikan'), name: "Kecantikan", icon: 'heart', color: "#E84393" },
  { id: generateId('kategori', 'otomotif'), name: "Otomotif", icon: 'car', color: "#2ECC71" },
  { id: generateId('kategori', 'rumah'), name: "Rumah", icon: 'home', color: "#F39C12" },
  { id: generateId('kategori', 'pendidikan'), name: "Pendidikan", icon: 'book', color: "#4834DF" }
]

// Data subkategori - sama seperti Velozio
const subCategories: { [key: string]: Category[] } = {
  "Elektronik": [
    { id: generateId('sub', 'smartphone'), name: "Smartphone", icon: 'device-mobile' },
    { id: generateId('sub', 'laptop'), name: "Laptop", icon: 'device-mobile' },
    { id: generateId('sub', 'tablet'), name: "Tablet", icon: 'device-mobile' },
    { id: generateId('sub', 'headphone'), name: "Headphone", icon: 'device-mobile' },
    { id: generateId('sub', 'speaker'), name: "Speaker", icon: 'device-mobile' },
    { id: generateId('sub', 'smartwatch'), name: "Smartwatch", icon: 'device-mobile' },
    { id: generateId('sub', 'powerbank'), name: "Power Bank", icon: 'device-mobile' },
    { id: generateId('sub', 'charger'), name: "Charger", icon: 'device-mobile' }
  ],
  "Fashion": [
    { id: generateId('sub', 'baju-pria'), name: "Baju Pria", icon: 'shirt' },
    { id: generateId('sub', 'baju-wanita'), name: "Baju Wanita", icon: 'shirt' },
    { id: generateId('sub', 'celana'), name: "Celana", icon: 'shirt' },
    { id: generateId('sub', 'sepatu'), name: "Sepatu", icon: 'shirt' },
    { id: generateId('sub', 'tas'), name: "Tas", icon: 'shirt' },
    { id: generateId('sub', 'aksesoris'), name: "Aksesoris", icon: 'shirt' },
    { id: generateId('sub', 'jam-tangan'), name: "Jam Tangan", icon: 'shirt' },
    { id: generateId('sub', 'kacamata'), name: "Kacamata", icon: 'shirt' }
  ],
  "Makanan": [
    { id: generateId('sub', 'makanan-ringan'), name: "Makanan Ringan", icon: 'utensils' },
    { id: generateId('sub', 'minuman'), name: "Minuman", icon: 'utensils' },
    { id: generateId('sub', 'makanan-beku'), name: "Makanan Beku", icon: 'utensils' },
    { id: generateId('sub', 'bumbu-dapur'), name: "Bumbu Dapur", icon: 'utensils' },
    { id: generateId('sub', 'buah-sayur'), name: "Buah & Sayur", icon: 'utensils' },
    { id: generateId('sub', 'daging-ikan'), name: "Daging & Ikan", icon: 'utensils' },
    { id: generateId('sub', 'roti-kue'), name: "Roti & Kue", icon: 'utensils' },
    { id: generateId('sub', 'susu-telur'), name: "Susu & Telur", icon: 'utensils' }
  ]
}

// Komponen CategoryItem - icon sama untuk mobile dan desktop
const CategoryItem = ({ category, onClick, isExpandedView = false }: {
  category: Category,
  onClick?: (e: React.MouseEvent<Element>) => void,
  isExpandedView?: boolean
}) => (
  <motion.div
    className={`
      flex flex-col items-center justify-center rounded-lg transition-all duration-200
      border border-gray-200 bg-white hover:bg-gray-50 shadow-sm
      ${isExpandedView ? 'p-2 w-[100px] h-[100px]' : 'p-1.5 w-[80px] h-[80px] mx-0.5'}
      overflow-hidden cursor-pointer
    `}
    initial={{ opacity: 0, y: 5 }}
    animate={{ opacity: 1, y: 0 }}
    whileHover={{ scale: 1.02 }}
    whileTap={{ scale: 0.98 }}
    onClick={(e: React.MouseEvent) => {
      e.preventDefault()
      onClick?.(e)
    }}
  >
    <div className={`flex items-center justify-center ${isExpandedView ? 'mb-2' : 'mb-1'}`} style={{ height: '32px', width: '100%' }}>
      <div
        className="flex items-center justify-center w-8 h-8"
        style={{ color: category.color, minWidth: '32px' }}
      >
        {getIcon(category.icon)}
      </div>
    </div>
    <div className="w-full px-1 flex justify-center">
      <span className={`
        text-center text-gray-800 leading-tight break-words
        ${isExpandedView ? 'text-xs' : 'text-[10px] line-clamp-2'}
        w-full
      `}>
        {category.name}
      </span>
    </div>
  </motion.div>
)

// Komponen SeeAllItem
const SeeAllItem = ({ onClick, isExpanded }: { onClick: (e: React.MouseEvent) => void, isExpanded: boolean }) => (
  <motion.div
    className="flex flex-col items-center justify-center rounded-lg transition-all duration-200
               border border-gray-200 bg-white hover:bg-gray-50 shadow-sm
               p-1.5 w-[80px] h-[80px] mx-0.5 overflow-hidden cursor-pointer"
    initial={{ opacity: 0, y: 5 }}
    animate={{ opacity: 1, y: 0 }}
    whileHover={{ scale: 1.02 }}
    whileTap={{ scale: 0.98 }}
    onClick={onClick}
  >
    <div className="flex items-center justify-center mb-1" style={{ height: '32px', width: '100%' }}>
      <div className="flex items-center justify-center w-8 h-8" style={{ color: '#666', minWidth: '32px' }}>
        <Grid3X3 size={20} />
      </div>
    </div>
    <div className="w-full px-1 flex justify-center">
      <span className="text-center text-gray-800 leading-tight break-words text-[10px] line-clamp-2 w-full">
        Lihat Semua
      </span>
    </div>
  </motion.div>
)

export default function SellzioCategories() {
  const [isExpanded, setIsExpanded] = useState(false)
  const [visibleCategories, setVisibleCategories] = useState(allCategories.slice(0, 9))
  const [selectedCategory, setSelectedCategory] = useState<Category | null>(null)
  const [showSubcategoryView, setShowSubcategoryView] = useState(false)
  const [sortedCategories, setSortedCategories] = useState(allCategories)
  const [isMobile, setIsMobile] = useState(false)
  const mobileContainerRef = useRef<HTMLDivElement>(null)

  // Detect mobile
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768)
    }

    checkMobile()
    window.addEventListener('resize', checkMobile)
    return () => window.removeEventListener('resize', checkMobile)
  }, [])

  // Fungsi untuk menangani klik kategori - sama seperti Velozio
  const handleCategoryClick = (e: React.MouseEvent, category: Category) => {
    e.preventDefault();
    if (!category) return;

    // Untuk tampilan mobile, buka subcategory view
    if (isMobile) {
      // Update urutan kategori dengan yang dipilih di paling atas
      const newSortedCategories = [
        category,
        ...allCategories.filter((cat: Category) => cat.id !== category.id)
      ];
      setSortedCategories(newSortedCategories);

      setSelectedCategory(category);
      setShowSubcategoryView(true);
      // Disable scrolling pada body saat subcategory view terbuka
      document.body.style.overflow = 'hidden';
      return;
    }

    const hasSubcategories = category.name in subCategories &&
      subCategories[category.name]?.length > 0;

    if (hasSubcategories) {
      setSelectedCategory(category);
    } else {
      // Jika tidak ada subkategori, lakukan aksi default
      console.log('Kategori dipilih:', category.name);
      // Tambahkan logika navigasi atau tindakan lain yang diperlukan
    }
  };

  // Fungsi untuk toggle expand - sama seperti Velozio
  const toggleExpand = (e: React.MouseEvent) => {
    e.preventDefault();

    // Untuk mobile, langsung ke expanded view
    if (isMobile) {
      setIsExpanded(true);
      return;
    }

    // Untuk desktop/tablet, tetap gunakan logika expand biasa
    const newExpandedState = !isExpanded;
    setIsExpanded(newExpandedState);

    if (newExpandedState && mobileContainerRef.current) {
      mobileContainerRef.current.scrollLeft = 0;
    }

    setVisibleCategories(newExpandedState ? allCategories : allCategories.slice(0, 9));
  };

  // Fungsi untuk menutup subcategory view
  const handleCloseSubcategoryView = () => {
    setShowSubcategoryView(false);
    // Enable scrolling kembali pada body
    document.body.style.overflow = '';
  };

  // Mengambil daftar subkategori
  const getSubcategories = (categoryName: string) => {
    if (!categoryName || !(categoryName in subCategories)) {
      console.log('Kategori tidak ditemukan atau tidak valid:', categoryName);
      return [];
    }
    return subCategories[categoryName];
  };

  // Fungsi untuk menangani klik tombol kembali
  const handleBackClick = () => {
    setSelectedCategory(null);
  };

  // Fungsi untuk menangani klik subkategori
  const handleSubcategoryClick = (subcategory: Category) => {
    if (!subcategory) return;

    console.log('Subkategori dipilih:', subcategory.name);
    // Tambahkan logika navigasi atau tindakan lain yang diperlukan
  };

  return (
    <div className="sellzio-categories-section">
      {/* Desktop Subcategory View */}
      {selectedCategory && !isMobile && (
        <div className="desktop-subcategory-overlay">
          <div className="subcategory-container">
            <div className="category-sidebar">
              {allCategories.map((cat: Category) => (
                <div
                  key={cat.id}
                  className={`sidebar-item ${cat.id === selectedCategory?.id ? 'active' : ''}`}
                  onClick={() => setSelectedCategory(cat)}
                >
                  <div className="sidebar-icon" style={{ color: cat.id === selectedCategory?.id ? (cat.color || '#ee4d2d') : '#666' }}>
                    {getIcon(cat.icon)}
                  </div>
                  <div className="sidebar-text">{cat.name}</div>
                </div>
              ))}
            </div>

            <div className="subcategory-content">
              <div className="subcategory-header">
                <button className="back-button" onClick={handleBackClick}>
                  <ChevronLeft size={20} />
                  <span>Kembali</span>
                </button>
                <h3 className="subcategory-title">{selectedCategory.name}</h3>
              </div>

              <div className="subcategory-grid">
                {subCategories[selectedCategory.name]?.map((subCategory: Category, idx: number) => (
                  <div
                    key={idx}
                    className="subcategory-item"
                    onClick={() => handleSubcategoryClick(subCategory)}
                  >
                    <div className="subcategory-icon" style={{ color: selectedCategory.color }}>
                      {getIcon(subCategory.icon)}
                    </div>
                    <div className="subcategory-title">
                      {subCategory.name}
                    </div>
                  </div>
                )) || (
                  <div className="col-span-3 text-center py-8 text-gray-500">
                    Belum ada subkategori untuk kategori ini.
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      )}

      <div className="container mx-auto max-w-6xl px-0 overflow-visible">
        {/* Mobile View */}
        {isMobile ? (
          <>
            <div className={`relative categories-container-wrapper ${!isExpanded ? 'has-blur' : ''}`}>
              {!isExpanded && (
                <div
                  ref={mobileContainerRef}
                  className="flex overflow-x-auto pb-3 hide-scrollbar categories-container"
                  style={{
                    scrollbarWidth: 'none',
                    msOverflowStyle: 'none',
                    WebkitOverflowScrolling: 'touch',
                    paddingRight: '5px'
                  }}
                >
                  <div className="flex gap-2 relative pr-4">
                    <AnimatePresence>
                      {visibleCategories.map((category, index) => (
                        <CategoryItem
                          key={index}
                          category={category}
                          onClick={(e) => handleCategoryClick(e, category)}
                        />
                      ))}
                      {!isExpanded && (
                        <div className="pr-2">
                          <SeeAllItem
                            onClick={(e: React.MouseEvent) => toggleExpand(e)}
                            isExpanded={isExpanded}
                          />
                        </div>
                      )}
                    </AnimatePresence>
                  </div>
                </div>
              )}
            </div>

            {/* Mobile Expanded View */}
            {isExpanded && !showSubcategoryView && (
              <div
                className="fixed inset-0 bg-white z-50 overflow-y-auto"
                style={{ paddingTop: '60px' }}
              >
                <div
                  className="grid grid-flow-col auto-cols-[100px] gap-3 justify-start w-max mx-auto"
                  style={{
                    gridAutoFlow: 'column',
                    gridTemplateRows: 'repeat(2, 100px)',
                    padding: '0.5rem 1rem',
                    margin: '0 auto',
                    width: 'auto',
                    minWidth: '100%',
                    display: 'inline-grid'
                  } as React.CSSProperties}
                >
                  <AnimatePresence>
                    {allCategories.map((category, index) => (
                      <CategoryItem
                        key={index}
                        category={category}
                        isExpandedView={true}
                        onClick={(e) => handleCategoryClick(e, category)}
                      />
                    ))}
                  </AnimatePresence>
                </div>
              </div>
            )}

            {/* Mobile Subcategory View */}
            {showSubcategoryView && selectedCategory && (
              <SubcategoryView
                category={selectedCategory}
                subcategories={getSubcategories(selectedCategory.name)}
                onBack={handleCloseSubcategoryView}
                onSubcategoryClick={handleSubcategoryClick}
              />
            )}
          </>
        ) : (
          /* Desktop View */
          <div className="desktop-categories-grid">
            {allCategories.map((category, index) => (
              <CategoryItem
                key={`desktop-${category.id}-${index}`}
                category={category}
                onClick={(e) => handleCategoryClick(e, category)}
                isExpandedView={false}
              />
            ))}
          </div>
        )}
      </div>
    </div>
  )
}
