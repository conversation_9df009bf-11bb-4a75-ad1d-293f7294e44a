'use client'

import React, { useState, useRef, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import {
  ChevronLeft
} from 'lucide-react'
import SubcategoryView from './SubcategoryView'

// Interface untuk Category
interface Category {
  id: string;
  name: string;
  icon: string;
  color?: string;
  slug?: string;
  banner?: string;
  subcategories?: Category[];
}

// Interface untuk SubcategoryView
interface SubcategoryViewProps {
  category: Category;
  subcategories: Category[];
  onBack: () => void;
  onSubcategoryClick: (subcategory: Category) => void;
}

// Fungsi untuk generate ID
const generateId = (prefix: string, name: string) => {
  return `${prefix}-${name.toLowerCase().replace(/\s+/g, '-')}`
}

// Fungsi untuk mendapatkan icon - menggunakan emoji seperti SubcategoryView
const getIcon = (iconName: string) => {
  const iconMap: { [key: string]: string } = {
    'device-mobile': '📱',
    'shirt': '👕',
    'utensils': '🍽️',
    'shopping-cart': '🛒',
    'gamepad': '🎮',
    'map-pin': '📍',
    'heart': '💄',
    'car': '🚗',
    'monitor': '💻',
    'camera': '📷',
    'home': '🏠',
    'kesehatan': '🏥',
    'activity': '⚽',
    'gift': '🧸',
    'smile': '👶',
    'book': '📚'
  }
  return iconMap[iconName] || '🛒'
}

// Data kategori - lengkap sesuai referensi Velozio
const allCategories: Category[] = [
  { id: generateId('kategori', 'elektronik'), name: "Elektronik", icon: 'device-mobile', color: "#1BA0E2" },
  { id: generateId('kategori', 'fashion'), name: "Fashion", icon: 'shirt', color: "#F1C40F" },
  { id: generateId('kategori', 'makanan'), name: "Makanan", icon: 'utensils', color: "#F25C05" },
  { id: generateId('kategori', 'belanja'), name: "Belanja", icon: 'shopping-cart', color: "#E74C3C" },
  { id: generateId('kategori', 'game'), name: "Game", icon: 'gamepad', color: "#3498DB" },
  { id: generateId('kategori', 'lokal'), name: "Lokal", icon: 'map-pin', color: "#9B59B6" },
  { id: generateId('kategori', 'kecantikan'), name: "Kecantikan", icon: 'heart', color: "#E84393" },
  { id: generateId('kategori', 'otomotif'), name: "Otomotif", icon: 'car', color: "#2C3E50" },
  { id: generateId('kategori', 'komputer'), name: "Komputer", icon: 'monitor', color: "#5D6D7E" },
  { id: generateId('kategori', 'hobi'), name: "Hobi", icon: 'camera', color: "#1ABC9C" },
  { id: generateId('kategori', 'rumah'), name: "Rumah", icon: 'home', color: "#8E44AD" },
  { id: generateId('kategori', 'kesehatan'), name: "Kesehatan", icon: 'kesehatan', color: "#2ECC71" },
  { id: generateId('kategori', 'olahraga'), name: "Olahraga", icon: 'activity', color: "#E67E22" },
  { id: generateId('kategori', 'mainan'), name: "Mainan", icon: 'gift', color: "#FF6B81" },
  { id: generateId('kategori', 'bayi'), name: "Bayi", icon: 'smile', color: "#FDA7DF" },
  { id: generateId('kategori', 'pendidikan'), name: "Pendidikan", icon: 'book', color: "#4834DF" }
]

// Data subkategori - lengkap sesuai referensi Velozio
const subCategories: { [key: string]: Category[] } = {
  "Elektronik": [
    { id: generateId('elektronik', 'handphone'), name: "Handphone", icon: 'device-mobile', color: "#1BA0E2" },
    { id: generateId('elektronik', 'laptop'), name: "Laptop", icon: 'monitor', color: "#1BA0E2" },
    { id: generateId('elektronik', 'tablet'), name: "Tablet", icon: 'device-mobile', color: "#1BA0E2" },
    { id: generateId('elektronik', 'kamera'), name: "Kamera", icon: 'camera', color: "#1BA0E2" },
    { id: generateId('elektronik', 'audio'), name: "Audio", icon: 'headphones', color: "#1BA0E2" },
    { id: generateId('elektronik', 'aksesoris'), name: "Aksesoris", icon: 'device-mobile', color: "#1BA0E2" },
    { id: generateId('elektronik', 'smart-home'), name: "Smart Home", icon: 'home', color: "#1BA0E2" },
    { id: generateId('elektronik', 'gadget'), name: "Gadget", icon: 'device-mobile', color: "#1BA0E2" }
  ],
  "Fashion": [
    { id: generateId('fashion', 'pria'), name: "Pria", icon: 'shirt', color: "#F1C40F" },
    { id: generateId('fashion', 'wanita'), name: "Wanita", icon: 'shirt', color: "#F1C40F" },
    { id: generateId('fashion', 'anak'), name: "Anak", icon: 'shirt', color: "#F1C40F" },
    { id: generateId('fashion', 'muslim'), name: "Muslim", icon: 'shirt', color: "#F1C40F" },
    { id: generateId('fashion', 'tas'), name: "Tas", icon: 'shopping-cart', color: "#F1C40F" },
    { id: generateId('fashion', 'sepatu'), name: "Sepatu", icon: 'shopping-cart', color: "#F1C40F" },
    { id: generateId('fashion', 'jam-tangan'), name: "Jam Tangan", icon: 'shopping-cart', color: "#F1C40F" },
    { id: generateId('fashion', 'aksesoris'), name: "Aksesoris", icon: 'shopping-cart', color: "#F1C40F" }
  ],
  "Makanan": [
    { id: generateId('makanan', 'makanan-instan'), name: "Makanan Instan", icon: 'makanan', color: "#F25C05" },
    { id: generateId('makanan', 'minuman'), name: "Minuman", icon: 'makanan', color: "#F25C05" },
    { id: generateId('makanan', 'bumbu-dapur'), name: "Bumbu Dapur", icon: 'makanan', color: "#F25C05" },
    { id: generateId('makanan', 'kue-camilan'), name: "Kue & Camilan", icon: 'makanan', color: "#F25C05" },
    { id: generateId('makanan', 'bahan-pokok'), name: "Bahan Pokok", icon: 'makanan', color: "#F25C05" },
    { id: generateId('makanan', 'makanan-kaleng'), name: "Makanan Kaleng", icon: 'makanan', color: "#F25C05" }
  ],
  "Belanja": [
    { id: generateId('belanja', 'bulanan'), name: "Belanja Bulanan", icon: 'shopping-bag', color: "#E74C3C" },
    { id: generateId('belanja', 'harian'), name: "Kebutuhan Harian", icon: 'shopping-cart', color: "#E74C3C" },
    { id: generateId('belanja', 'promo'), name: "Promo Spesial", icon: 'tag', color: "#E74C3C" }
  ],
  "Game": [
    { id: generateId('game', 'mobile'), name: "Game Mobile", icon: 'smartphone', color: "#3498DB" },
    { id: generateId('game', 'pc'), name: "Game PC", icon: 'monitor', color: "#3498DB" },
    { id: generateId('game', 'aksesori'), name: "Aksesori Gaming", icon: 'headphones', color: "#3498DB" }
  ],
  "Lokal": [
    { id: generateId('subkategori', 'produk-umkm'), name: "Produk UMKM", icon: 'home', color: "#9B59B6" },
    { id: generateId('subkategori', 'kerajinan-lokal'), name: "Kerajinan Lokal", icon: 'tool', color: "#9B59B6" },
    { id: generateId('subkategori', 'makanan-khas'), name: "Makanan Khas", icon: 'utensils', color: "#9B59B6" }
  ],
  "Kecantikan": [
    { id: generateId('subkategori', 'skincare'), name: "Skincare", icon: 'droplet', color: "#E84393" },
    { id: generateId('subkategori', 'makeup'), name: "Makeup", icon: 'smile', color: "#E84393" },
    { id: generateId('subkategori', 'perawatan-rambut'), name: "Perawatan Rambut", icon: 'scissors', color: "#E84393" }
  ],
  "Otomotif": [
    { id: generateId('subkategori', 'aksesoris-mobil'), name: "Aksesoris Mobil", icon: 'car', color: "#2C3E50" },
    { id: generateId('subkategori', 'aksesoris-motor'), name: "Aksesoris Motor", icon: 'activity', color: "#2C3E50" },
    { id: generateId('subkategori', 'perawatan-kendaraan'), name: "Perawatan Kendaraan", icon: 'tool', color: "#2C3E50" }
  ],
  "Komputer": [
    { id: generateId('subkategori', 'laptop'), name: "Laptop", icon: 'laptop', color: "#5D6D7E" },
    { id: generateId('subkategori', 'pc-desktop'), name: "PC Desktop", icon: 'monitor', color: "#5D6D7E" },
    { id: generateId('subkategori', 'aksesoris-komputer'), name: "Aksesoris Komputer", icon: 'keyboard', color: "#5D6D7E" }
  ],
  "Hobi": [
    { id: generateId('subkategori', 'fotografi'), name: "Fotografi", icon: 'camera', color: "#1ABC9C" },
    { id: generateId('subkategori', 'musik'), name: "Musik", icon: 'music', color: "#1ABC9C" },
    { id: generateId('subkategori', 'koleksi'), name: "Koleksi", icon: 'archive', color: "#1ABC9C" }
  ],
  "Rumah": [
    { id: generateId('subkategori', 'furniture'), name: "Furniture", icon: 'inbox', color: "#8E44AD" },
    { id: generateId('subkategori', 'dekorasi'), name: "Dekorasi", icon: 'image', color: "#8E44AD" },
    { id: generateId('subkategori', 'perlengkapan-dapur'), name: "Perlengkapan Dapur", icon: 'coffee', color: "#8E44AD" }
  ],
  "Kesehatan": [
    { id: generateId('subkategori', 'obat-obatan'), name: "Obat-obatan", icon: 'thermometer', color: "#2ECC71" },
    { id: generateId('subkategori', 'suplemen'), name: "Suplemen", icon: 'package', color: "#2ECC71" },
    { id: generateId('subkategori', 'alat-kesehatan'), name: "Alat Kesehatan", icon: 'activity', color: "#2ECC71" }
  ],
  "Olahraga": [
    { id: generateId('subkategori', 'pakaian-olahraga'), name: "Pakaian Olahraga", icon: 'shirt', color: "#E67E22" },
    { id: generateId('subkategori', 'alat-olahraga'), name: "Alat Olahraga", icon: 'dumbbell', color: "#E67E22" },
    { id: generateId('subkategori', 'sepatu-olahraga'), name: "Sepatu Olahraga", icon: 'boot', color: "#E67E22" }
  ],
  "Mainan": [
    { id: generateId('subkategori', 'mainan-anak'), name: "Mainan Anak", icon: 'truck', color: "#FF6B81" },
    { id: generateId('subkategori', 'mainan-edukatif'), name: "Mainan Edukatif", icon: 'book', color: "#FF6B81" },
    { id: generateId('subkategori', 'action-figure'), name: "Action Figure", icon: 'star', color: "#FF6B81" }
  ],
  "Bayi": [
    { id: generateId('subkategori', 'pakaian-bayi'), name: "Pakaian Bayi", icon: 'shirt', color: "#FDA7DF" },
    { id: generateId('subkategori', 'perlengkapan-bayi'), name: "Perlengkapan Bayi", icon: 'package', color: "#FDA7DF" },
    { id: generateId('subkategori', 'makanan-bayi'), name: "Makanan Bayi", icon: 'coffee', color: "#FDA7DF" }
  ],
  "Pendidikan": [
    { id: generateId('subkategori', 'buku'), name: "Buku", icon: 'book', color: "#4834DF" },
    { id: generateId('subkategori', 'alat-tulis'), name: "Alat Tulis", icon: 'edit', color: "#4834DF" },
    { id: generateId('subkategori', 'kursus-online'), name: "Kursus Online", icon: 'monitor', color: "#4834DF" }
  ]
}

// Komponen CategoryItem - icon sama untuk mobile dan desktop
const CategoryItem = ({ category, onClick, isExpandedView = false }: {
  category: Category,
  onClick?: (e: React.MouseEvent<Element>) => void,
  isExpandedView?: boolean
}) => (
  <motion.div
    className={`
      flex flex-col items-center justify-center rounded-lg transition-all duration-200
      border border-gray-200 bg-white hover:bg-gray-50 shadow-sm
      ${isExpandedView ? 'p-2 w-[100px] h-[100px]' : 'p-1.5 w-[80px] h-[80px] mx-0.5'}
      overflow-hidden cursor-pointer
    `}
    initial={{ opacity: 0, y: 5 }}
    animate={{ opacity: 1, y: 0 }}
    whileHover={{ scale: 1.02 }}
    whileTap={{ scale: 0.98 }}
    onClick={(e: React.MouseEvent) => {
      e.preventDefault()
      onClick?.(e)
    }}
  >
    <div className={`flex items-center justify-center ${isExpandedView ? 'mb-2' : 'mb-1'}`} style={{ height: '32px', width: '100%' }}>
      <div
        className="flex items-center justify-center w-8 h-8 text-2xl"
        style={{ minWidth: '32px' }}
      >
        {getIcon(category.icon)}
      </div>
    </div>
    <div className="w-full px-1 flex justify-center">
      <span className={`
        text-center text-gray-800 leading-tight break-words
        ${isExpandedView ? 'text-xs' : 'text-[10px] line-clamp-2'}
        w-full
      `}>
        {category.name}
      </span>
    </div>
  </motion.div>
)

// Komponen SeeAllItem
const SeeAllItem = ({ onClick, isExpanded }: { onClick: (e: React.MouseEvent) => void, isExpanded: boolean }) => (
  <motion.div
    className="flex flex-col items-center justify-center rounded-lg transition-all duration-200
               border border-gray-200 bg-white hover:bg-gray-50 shadow-sm
               p-1.5 w-[80px] h-[80px] mx-0.5 overflow-hidden cursor-pointer"
    initial={{ opacity: 0, y: 5 }}
    animate={{ opacity: 1, y: 0 }}
    whileHover={{ scale: 1.02 }}
    whileTap={{ scale: 0.98 }}
    onClick={onClick}
  >
    <div className="flex items-center justify-center mb-1" style={{ height: '32px', width: '100%' }}>
      <div className="flex items-center justify-center w-8 h-8 text-2xl" style={{ minWidth: '32px' }}>
        ⊡
      </div>
    </div>
    <div className="w-full px-1 flex justify-center">
      <span className="text-center text-gray-800 leading-tight break-words text-[10px] line-clamp-2 w-full">
        Lihat Semua
      </span>
    </div>
  </motion.div>
)

export default function SellzioCategories() {
  const [isExpanded, setIsExpanded] = useState(false)
  const [visibleCategories, setVisibleCategories] = useState(allCategories.slice(0, 9))
  const [selectedCategory, setSelectedCategory] = useState<Category | null>(null)
  const [showSubcategoryView, setShowSubcategoryView] = useState(false)
  const [sortedCategories, setSortedCategories] = useState(allCategories)
  const [showScrollHint, setShowScrollHint] = useState(false)
  const [isScrolling, setIsScrolling] = useState(false)
  const scrollTimeoutRef = useRef<NodeJS.Timeout | null>(null)
  const initialShowTimeoutRef = useRef<NodeJS.Timeout | null>(null)
  const hideTimeoutRef = useRef<NodeJS.Timeout | null>(null)
  const showIntervalRef = useRef<NodeJS.Timeout | null>(null)
  const [isMobile, setIsMobile] = useState(false)
  const mobileContainerRef = useRef<HTMLDivElement>(null)

  // Detect mobile
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768)
    }

    checkMobile()
    window.addEventListener('resize', checkMobile)
    return () => window.removeEventListener('resize', checkMobile)
  }, [])

  // Set up initial show timeout dan interval untuk scroll hint
  useEffect(() => {
    if (isMobile) {
      // Tampilkan tombol pertama kali setelah 7 detik
      initialShowTimeoutRef.current = setTimeout(() => {
        if (!isExpanded) {
          showViewAllButton();
        }

        // Set interval untuk menampilkan tombol setiap 15 detik
        showIntervalRef.current = setInterval(() => {
          if (!isScrolling && !isExpanded) {
            showViewAllButton();
          }
        }, 15000);
      }, 7000);

      // Add scroll event listener
      const container = mobileContainerRef.current;
      if (container) {
        container.addEventListener('scroll', handleScroll);
      }

      // Cleanup function
      return () => {
        if (initialShowTimeoutRef.current) clearTimeout(initialShowTimeoutRef.current);
        if (scrollTimeoutRef.current) clearTimeout(scrollTimeoutRef.current);
        if (hideTimeoutRef.current) clearTimeout(hideTimeoutRef.current);
        if (showIntervalRef.current) clearInterval(showIntervalRef.current);
        if (container) container.removeEventListener('scroll', handleScroll);
      };
    }
  }, [isMobile, isScrolling, isExpanded])

  // Fungsi untuk menangani klik kategori - sama seperti Velozio
  const handleCategoryClick = (e: React.MouseEvent, category: Category) => {
    e.preventDefault();
    if (!category) return;

    // Untuk tampilan mobile, buka subcategory view
    if (isMobile) {
      // Update urutan kategori dengan yang dipilih di paling atas
      const newSortedCategories = [
        category,
        ...allCategories.filter((cat: Category) => cat.id !== category.id)
      ];
      setSortedCategories(newSortedCategories);

      setSelectedCategory(category);
      setShowSubcategoryView(true);
      // Disable scrolling pada body saat subcategory view terbuka
      document.body.style.overflow = 'hidden';
      return;
    }

    // Update urutan kategori dengan yang dipilih di paling atas untuk semua device
    const newSortedCategories = [
      category,
      ...allCategories.filter((cat: Category) => cat.id !== category.id)
    ];
    setSortedCategories(newSortedCategories);

    const hasSubcategories = category.name in subCategories &&
      subCategories[category.name]?.length > 0;

    if (hasSubcategories) {
      setSelectedCategory(category);
    } else {
      // Jika tidak ada subkategori, lakukan aksi default
      console.log('Kategori dipilih:', category.name);
      // Tambahkan logika navigasi atau tindakan lain yang diperlukan
    }
  };

  // Fungsi untuk toggle expand - sama seperti Velozio
  const toggleExpand = (e: React.MouseEvent) => {
    e.preventDefault();

    if (isMobile) {
      // Jika di mobile, langsung arahkan ke subcategory view dengan kategori pertama
      if (allCategories.length > 0) {
        const firstCategory = allCategories[0];
        const newSortedCategories = [
          firstCategory,
          ...allCategories.filter((cat: Category) => cat.id !== firstCategory.id)
        ];
        setSortedCategories(newSortedCategories);
        setSelectedCategory(firstCategory);
        setShowSubcategoryView(true);
        document.body.style.overflow = 'hidden';
      }
      return;
    }

    // Untuk desktop/tablet, tetap gunakan logika expand biasa
    const newExpandedState = !isExpanded;
    setIsExpanded(newExpandedState);

    if (newExpandedState && mobileContainerRef.current) {
      mobileContainerRef.current.scrollLeft = 0;
    }

    setVisibleCategories(newExpandedState ? allCategories : allCategories.slice(0, 9));
  };

  // Fungsi untuk menutup subcategory view
  const handleCloseSubcategoryView = () => {
    setShowSubcategoryView(false);
    // Enable scrolling kembali pada body
    document.body.style.overflow = '';
  };

  // Mengambil daftar subkategori
  const getSubcategories = (categoryName: string) => {
    if (!categoryName || !(categoryName in subCategories)) {
      console.log('Kategori tidak ditemukan atau tidak valid:', categoryName);
      return [];
    }
    return subCategories[categoryName];
  };

  // Fungsi untuk menangani klik tombol kembali
  const handleBackClick = () => {
    setSelectedCategory(null);
  };

  // Fungsi untuk menangani klik subkategori
  const handleSubcategoryClick = (subcategory: Category) => {
    if (!subcategory) return;

    console.log('Subkategori dipilih:', subcategory.name);

    // Tutup subcategory view terlebih dahulu
    const currentCategory = selectedCategory;
    setSelectedCategory(null);
    setShowSubcategoryView(false);
    document.body.style.overflow = '';

    // Trigger search di halaman utama dengan subcategory name
    // Kita perlu memanggil fungsi search dari parent component
    if (typeof window !== 'undefined') {
      // Dispatch custom event untuk komunikasi dengan parent
      const searchEvent = new CustomEvent('subcategorySearch', {
        detail: {
          query: subcategory.name,
          category: currentCategory?.name,
          selectedSubcategory: subcategory.name,
          allSubcategories: getSubcategories(currentCategory?.name || '')
        }
      });
      window.dispatchEvent(searchEvent);
    }
  };

  // Animasi scroll hint untuk mobile
  const mobileScrollHintAnimation = {
    x: [0, 3, 0],
    transition: {
      repeat: Infinity,
      duration: 1.5,
      repeatType: "loop" as const,
      ease: "easeInOut"
    }
  };

  // Fungsi untuk menampilkan tombol View All
  const showViewAllButton = () => {
    // Jangan tampilkan tombol jika dalam mode expanded
    if (isExpanded) {
      setShowScrollHint(false);
      return;
    }

    // Hapus timeout yang ada
    if (hideTimeoutRef.current) clearTimeout(hideTimeoutRef.current);

    // Tampilkan tombol
    setShowScrollHint(true);

    // Sembunyikan setelah 5 detik
    hideTimeoutRef.current = setTimeout(() => {
      setShowScrollHint(false);
    }, 5000);
  };

  // Handle scroll untuk mobile
  const handleScroll = () => {
    if (!mobileContainerRef.current || isExpanded) return;

    const { scrollLeft, scrollWidth, clientWidth } = mobileContainerRef.current;
    const isAtEnd = scrollLeft + clientWidth >= scrollWidth - 5; // Sedikit toleransi

    // Sembunyikan tombol View All saat scroll
    setShowScrollHint(false);
    setIsScrolling(true);

    // Hapus semua timeout yang ada
    if (scrollTimeoutRef.current) clearTimeout(scrollTimeoutRef.current);
    if (hideTimeoutRef.current) clearTimeout(hideTimeoutRef.current);

    // Set timeout untuk menampilkan tombol View All setelah 15 detik tidak ada scroll
    scrollTimeoutRef.current = setTimeout(() => {
      setIsScrolling(false);
      if (!isAtEnd && !isExpanded) { // Hanya tampilkan jika tidak di ujung kanan dan tidak expanded
        showViewAllButton();
      }
    }, 15000);
  };

  return (
    <div className="sellzio-categories-section">
      {/* Desktop Subcategory View */}
      {selectedCategory && !isMobile && (
        <div
          className="desktop-subcategory-overlay"
          onClick={(e) => {
            // Close popup jika click di luar container
            if (e.target === e.currentTarget) {
              setSelectedCategory(null);
            }
          }}
        >
          <div className="subcategory-container">
            {/* Sidebar Kategori dengan Sticky Header */}
            <div className="category-sidebar">
              {/* Sticky Header di atas kategori */}
              <div className="sidebar-sticky-header">
                <button className="back-button" onClick={handleBackClick}>
                  <ChevronLeft size={20} />
                  <span>Kembali</span>
                </button>
              </div>

              {/* Daftar Kategori dengan Reordering untuk Sidebar */}
              <div className="sidebar-categories">
                {sortedCategories.map((cat: Category) => (
                  <div
                    key={cat.id}
                    className={`sidebar-item ${cat.id === selectedCategory?.id ? 'active' : ''}`}
                    onClick={() => {
                      // Update urutan kategori hanya untuk sidebar
                      const newSortedCategories = [
                        cat,
                        ...sortedCategories.filter((c: Category) => c.id !== cat.id)
                      ];
                      setSortedCategories(newSortedCategories);
                      setSelectedCategory(cat);
                    }}
                  >
                    <div className="sidebar-icon text-xl">
                      {getIcon(cat.icon)}
                    </div>
                    <div className="sidebar-text">{cat.name}</div>
                  </div>
                ))}
              </div>
            </div>

            {/* Content Area */}
            <div className="subcategory-content">
              {/* Header dengan Close Button */}
              <div className="subcategory-header">
                <h3 className="subcategory-title">{selectedCategory.name}</h3>
              </div>

              {/* Close Button di pojok kanan atas */}
              <button
                className="close-button-top-right"
                onClick={() => setSelectedCategory(null)}
                aria-label="Tutup"
              >
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <line x1="18" y1="6" x2="6" y2="18"></line>
                  <line x1="6" y1="6" x2="18" y2="18"></line>
                </svg>
              </button>

              {/* Subcategory Grid */}
              <div className="subcategory-grid">
                {getSubcategories(selectedCategory.name).map((subCategory: Category, idx: number) => {
                  // Icon mapping untuk subkategori - sama seperti sebelumnya
                  const subcategoryIcons: { [key: string]: string } = {
                    // Elektronik
                    'Handphone': '📱', 'Laptop': '💻', 'Tablet': '📱', 'Kamera': '📷',
                    'Audio': '🎧', 'Aksesoris': '🔌', 'Smart Home': '🏠', 'Gadget': '📱',
                    // Fashion
                    'Pria': '👔', 'Wanita': '👗', 'Anak': '👶', 'Muslim': '🧕',
                    'Tas': '👜', 'Sepatu': '👟', 'Jam Tangan': '⌚', 'Aksesoris Fashion': '💍',
                    // Makanan
                    'Makanan Instan': '🍜', 'Minuman': '🥤', 'Bumbu Dapur': '🧂', 'Kue & Camilan': '🍪',
                    'Bahan Pokok': '🌾', 'Makanan Kaleng': '🥫',
                    // Belanja
                    'Belanja Bulanan': '🛒', 'Kebutuhan Harian': '🛍️', 'Promo Spesial': '🏷️',
                    // Game
                    'Game Mobile': '📱', 'Game PC': '💻', 'Aksesori Gaming': '🎮',
                    // Lokal
                    'Produk UMKM': '🏪', 'Kerajinan Lokal': '🎨', 'Makanan Khas': '🍲',
                    // Kecantikan
                    'Skincare': '🧴', 'Makeup': '💄', 'Perawatan Rambut': '💇',
                    // Otomotif
                    'Aksesoris Mobil': '🚗', 'Aksesoris Motor': '🏍️', 'Perawatan Kendaraan': '🔧',
                    // Komputer
                    'Laptop Komputer': '💻', 'PC Desktop': '🖥️', 'Aksesoris Komputer': '⌨️',
                    // Hobi
                    'Fotografi': '📷', 'Musik': '🎵', 'Koleksi': '🗃️',
                    // Rumah
                    'Furniture': '🪑', 'Dekorasi': '🖼️', 'Perlengkapan Dapur': '🍳',
                    // Kesehatan
                    'Obat-obatan': '💊', 'Suplemen': '💉', 'Alat Kesehatan': '🩺',
                    // Olahraga
                    'Pakaian Olahraga': '👕', 'Alat Olahraga': '🏋️', 'Sepatu Olahraga': '👟',
                    // Mainan
                    'Mainan Anak': '🧸', 'Mainan Edukatif': '🧩', 'Action Figure': '🦸',
                    // Bayi
                    'Pakaian Bayi': '👶', 'Perlengkapan Bayi': '🍼', 'Makanan Bayi': '🥛',
                    // Pendidikan
                    'Buku': '📚', 'Alat Tulis': '✏️', 'Kursus Online': '💻',
                    'default': '📦'
                  };

                  return (
                    <div
                      key={idx}
                      className="subcategory-item"
                      onClick={() => handleSubcategoryClick(subCategory)}
                    >
                      <div className="subcategory-icon text-2xl">
                        {subcategoryIcons[subCategory.name] || subcategoryIcons['default']}
                      </div>
                      <div className="subcategory-title">
                        {subCategory.name}
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          </div>
        </div>
      )}

      <div className="container mx-auto max-w-6xl px-0 overflow-visible">
        {/* Mobile View */}
        {isMobile ? (
          <>
            <div className={`relative categories-container-wrapper ${!isExpanded ? 'has-blur' : ''}`}>
              {!isExpanded && (
                <div
                  ref={mobileContainerRef}
                  className="flex overflow-x-auto pb-3 hide-scrollbar categories-container"
                  style={{
                    scrollbarWidth: 'none',
                    msOverflowStyle: 'none',
                    WebkitOverflowScrolling: 'touch',
                    paddingRight: '5px'
                  }}
                >
                  <div className="flex gap-2 relative pr-4">
                    <AnimatePresence>
                      {visibleCategories.map((category, index) => (
                        <CategoryItem
                          key={index}
                          category={category}
                          onClick={(e) => handleCategoryClick(e, category)}
                        />
                      ))}
                      {!isExpanded && (
                        <div className="pr-2">
                          <SeeAllItem
                            onClick={(e: React.MouseEvent) => toggleExpand(e)}
                            isExpanded={isExpanded}
                          />
                        </div>
                      )}
                    </AnimatePresence>
                  </div>

                  {/* Animasi panah untuk scroll hint di mobile */}
                  {showScrollHint && !isScrolling && (
                    <div
                      className="absolute right-2 top-[45%] -translate-y-1/2 z-10 h-8 bg-[rgba(255,245,240,0.95)] border border-[#FFDFD1] rounded-[16px] shadow-md flex items-center px-2 py-0 cursor-pointer"
                      onClick={(e: React.MouseEvent) => toggleExpand(e)}
                    >
                      <span className="text-[11px] font-semibold text-[#FF5722] mr-1">View All</span>
                      <motion.div
                        className="flex items-center justify-center text-[#FF5722]"
                        animate={mobileScrollHintAnimation}
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" width="15" height="15" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                          <polyline points="9 18 15 12 9 6"></polyline>
                        </svg>
                      </motion.div>
                    </div>
                  )}
                </div>
              )}
            </div>

            {/* Mobile Expanded View */}
            {isExpanded && !showSubcategoryView && (
              <div
                className="fixed inset-0 bg-white z-50 overflow-y-auto"
                style={{ paddingTop: '60px' }}
              >
                <div
                  className="grid grid-flow-col auto-cols-[100px] gap-3 justify-start w-max mx-auto"
                  style={{
                    gridAutoFlow: 'column',
                    gridTemplateRows: 'repeat(2, 100px)',
                    padding: '0.5rem 1rem',
                    margin: '0 auto',
                    width: 'auto',
                    minWidth: '100%',
                    display: 'inline-grid'
                  } as React.CSSProperties}
                >
                  <AnimatePresence>
                    {allCategories.map((category, index) => (
                      <CategoryItem
                        key={index}
                        category={category}
                        isExpandedView={true}
                        onClick={(e) => handleCategoryClick(e, category)}
                      />
                    ))}
                  </AnimatePresence>
                </div>
              </div>
            )}

            {/* Mobile Subcategory View */}
            {showSubcategoryView && selectedCategory && (
              <SubcategoryView
                category={selectedCategory}
                subcategories={getSubcategories(selectedCategory.name)}
                allCategories={allCategories}
                onBack={handleCloseSubcategoryView}
                onSubcategoryClick={handleSubcategoryClick}
                onCategoryChange={(newCategory) => {
                  setSelectedCategory(newCategory);
                  // Update urutan kategori dengan yang dipilih di paling atas
                  const newSortedCategories = [
                    newCategory,
                    ...allCategories.filter((cat: Category) => cat.id !== newCategory.id)
                  ];
                  setSortedCategories(newSortedCategories);
                }}
              />
            )}
          </>
        ) : (
          /* Desktop View */
          <div className="desktop-categories-grid">
            {allCategories.map((category, index) => (
              <CategoryItem
                key={`desktop-${category.id}-${index}`}
                category={category}
                onClick={(e) => handleCategoryClick(e, category)}
                isExpandedView={false}
              />
            ))}
          </div>
        )}
      </div>
    </div>
  )
}
