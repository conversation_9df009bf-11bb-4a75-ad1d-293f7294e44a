@import url("https://fonts.googleapis.com/css2?family=Roboto:wght@400;500;700&display=swap");

/* Reset dan base styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  font-family: '<PERSON><PERSON>', <PERSON><PERSON>, sans-serif;
}

html, body {
  width: 100%;
  overflow-x: hidden;
  margin: 0;
  padding: 0;
}

body {
  background-color: #f2f2f2;
  color: #333;
}

/* Header dan <PERSON> */
.header {
  background-color: #ee4d2d;
  padding: 10px 15px;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 1000;
  transition: all 0.3s ease;
  box-sizing: border-box;
}

.search-container {
  display: flex;
  align-items: center;
  position: relative;
  max-width: 800px;
  margin: 0 auto;
  width: 100%;
  box-sizing: border-box;
  overflow: visible;
}

.search-input-wrapper {
  position: relative;
  flex-grow: 1;
  margin-right: 85px;
}

/* Remove margin-right in expanded mode to allow full width */
.search-expanded .search-input-wrapper {
  margin-right: 0;
}

.search-input {
  width: 100%;
  padding: 10px 60px 10px 15px;
  border: 2px solid #ee4d2d;
  border-radius: 8px;
  font-size: 14px;
  outline: none;
  color: #333;
  background-color: #FFFFFF;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
  position: relative;
  z-index: 10;
}

/* Ensure search input uses full width in expanded mode */
.search-expanded .search-input {
  width: 100%;
  padding-right: 60px; /* Space for clear and search icons */
}

/* Placeholder warna oranye */
.search-input::placeholder {
  color: #ee4d2d;
  opacity: 1;
}

/* Animated placeholder */
.search-placeholder {
  position: absolute;
  left: 15px;
  top: 50%;
  transform: translateY(-50%);
  color: #ee4d2d;
  pointer-events: none;
  font-size: 14px;
  transition: opacity 0.3s;
  display: flex;
  flex-direction: column;
  width: calc(100% - 60px);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  height: 20px;
  z-index: 11;
}

.placeholder-static {
  white-space: nowrap;
  position: absolute;
  top: 0;
  left: 0;
  opacity: 1;
  animation: staticPlaceholderAnimation 12s infinite;
}

.placeholder-dynamic {
  position: relative;
  width: 100%;
  overflow: hidden;
  height: 20px;
  max-width: calc(100% - 60px);
  line-height: 1.2;
}

.placeholder-text {
  position: absolute;
  width: 100%;
  display: flex;
  align-items: center;
  opacity: 0;
  visibility: hidden;
  top: 0;
  left: 0;
  animation: placeholderAnimation 45s infinite;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

@keyframes staticPlaceholderAnimation {
  0%, 8% {
    opacity: 1;
  }
  12%, 96% {
    opacity: 0;
  }
  100% {
    opacity: 0;
  }
}

@keyframes placeholderAnimation {
  0% {
    opacity: 0;
    transform: translateY(20px);
    visibility: hidden;
  }
  1%, 5% {
    opacity: 1;
    transform: translateY(0);
    visibility: visible;
  }
  6%, 100% {
    opacity: 0;
    transform: translateY(-20px);
    visibility: hidden;
  }
}

.placeholder-text:nth-child(2) { animation-delay: 3s; }
.placeholder-text:nth-child(3) { animation-delay: 6s; }
.placeholder-text:nth-child(4) { animation-delay: 9s; }
.placeholder-text:nth-child(5) { animation-delay: 12s; }
.placeholder-text:nth-child(6) { animation-delay: 15s; }
.placeholder-text:nth-child(7) { animation-delay: 18s; }
.placeholder-text:nth-child(8) { animation-delay: 21s; }
.placeholder-text:nth-child(9) { animation-delay: 24s; }
.placeholder-text:nth-child(10) { animation-delay: 27s; }
.placeholder-text:nth-child(11) { animation-delay: 30s; }
.placeholder-text:nth-child(12) { animation-delay: 33s; }
.placeholder-text:nth-child(13) { animation-delay: 36s; }
.placeholder-text:nth-child(14) { animation-delay: 39s; }
.placeholder-text:nth-child(15) { animation-delay: 42s; }

/* Sembunyikan placeholder ketika input focus atau memiliki nilai */
.search-input:focus + .search-placeholder,
.search-input:not(:placeholder-shown) + .search-placeholder {
  opacity: 0 !important;
}

.search-icon {
  position: absolute;
  right: 15px;
  top: 50%;
  transform: translateY(-50%);
  color: #ee4d2d;
  cursor: pointer;
  font-size: 16px;
  z-index: 15;
  background: none;
  border: none;
  padding: 5px;
}

/* Clear search icon */
.clear-search-icon {
  position: absolute;
  right: 40px;
  top: 50%;
  transform: translateY(-50%);
  width: 20px;
  height: 20px;
  background-color: #999;
  border-radius: 50%;
  cursor: pointer;
  display: none;
  z-index: 35; /* Dinaikkan z-index-nya */
  align-items: center;
  justify-content: center;
  padding: 0;
  border: none;
  -webkit-tap-highlight-color: transparent;
  touch-action: manipulation;
  overflow: visible; /* Pastikan tidak ada yang terpotong */
}

.clear-search-icon::before {
  content: '';
  position: absolute;
  width: 24px; /* Diperkecil dari 40px */
  height: 24px; /* Diperkecil dari 40px */
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: transparent; /* Pastikan background transparan */
}

.clear-search-icon svg {
  color: white;
  pointer-events: none;
  width: 12px;
  height: 12px;
  flex-shrink: 0; /* Pastikan SVG tidak mengecil */
}

.clear-search-icon:hover {
  background-color: #aaa;
}

.clear-search-icon:active {
  background-color: #999;
  transform: translateY(-50%) scale(0.95);
}

/* Show clear icon when input has value */
.search-input:not(:placeholder-shown) ~ .clear-search-icon {
  display: flex;
}

/* Adjust clear icon position in expanded mode */
.search-expanded .clear-search-icon {
  right: 15px !important; /* Posisi lebih ke kanan */
  display: flex !important;
  z-index: 40;
}

/* Header Icons */
.header-icons {
  display: flex;
  align-items: center;
  position: absolute;
  right: 12px; /* Perbaiki posisi agar tidak terlalu ke kiri */
  top: 50%;
  transform: translateY(-50%);
  z-index: 20;
  gap: 15px;
  padding: 15px 5px; /* Tambah padding untuk ruang badge */
  overflow: visible; /* Pastikan badge tidak terpotong */
}

.cart-icon, .chat-icon {
  position: relative;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 22px;
  height: 22px;
}

.cart-icon svg, .chat-icon svg {
  width: 100%;
  height: 100%;
  overflow: visible; /* Mencegah pemotongan path SVG */
}

/* Badge notifikasi */
.cart-badge, .chat-badge {
  position: absolute;
  top: -12px;
  right: -6px;
  background-color: white;
  color: #ee4d2d;
  font-size: 11px;
  font-weight: bold;
  min-width: 20px;
  width: 20px;
  height: 20px;
  border-radius: 50%; /* Pastikan lingkaran sempurna */
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
  z-index: 30;
  box-shadow: 0 1px 3px rgba(0,0,0,0.3);
  line-height: 1;
  /* Memastikan teks benar-benar rata tengah */
  text-align: center;
  box-sizing: border-box;
  /* Menghilangkan spasi ekstra di sekitar teks */
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
  /* Pastikan tidak ada overflow yang memotong badge */
  overflow: visible;
}

/* Chat dots */
.chat-icon .chat-dots {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 60%;
  height: 20%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  z-index: 10;
}

.chat-icon .chat-dots .dot {
  width: 2px;
  height: 2px;
  background-color: white;
  border-radius: 50%;
  flex-shrink: 0;
}

.chat-icon .chat-dots .dot:nth-child(1) {
  animation: dotPulse 1.5s infinite ease-in-out;
}

.chat-icon .chat-dots .dot:nth-child(2) {
  animation: dotPulse 1.5s infinite 0.2s ease-in-out;
}

.chat-icon .chat-dots .dot:nth-child(3) {
  animation: dotPulse 1.5s infinite 0.4s ease-in-out;
}

@keyframes dotPulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.2);
  }
  100% {
    transform: scale(1);
  }
}

/* Search expanded mode */
.search-expanded {
  background-color: white;
  padding: 10px 15px 15px 15px;
  box-shadow: 0 2px 5px rgba(0,0,0,0.1);
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 1001;
  box-sizing: border-box;
}

.back-btn {
  margin-right: 10px;
  color: #ee4d2d;
  cursor: pointer;
  display: flex;
  align-items: center;
  background: none;
  border: none;
  padding: 5px;
}

.back-btn svg {
  transform: scaleX(1.3);
}

.expanded-search-icon {
  background-color: #ee4d2d;
  color: white;
  width: 40px;
  height: 40px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 10px;
  cursor: pointer;
  border: none;
}

/* Filter icon styling - sesuai docs/facet.html */
.filter-icon {
  background-color: #ee4d2d;
  color: white;
  width: 40px;
  height: 40px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 10px;
  cursor: pointer;
  border: none;
  position: relative;
  transition: all 0.2s ease;
}

.filter-icon i {
  font-size: 16px;
}

.filter-icon:hover {
  background-color: #d63916;
  transform: scale(1.05);
}

.filter-badge {
  position: absolute;
  top: -5px;
  right: -5px;
  background-color: #fff;
  color: #ee4d2d;
  border: 2px solid #ee4d2d;
  font-size: 11px;
  font-weight: bold;
  width: 18px;
  height: 18px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 25;
  text-align: center;
  line-height: 1;
  box-sizing: border-box;
  padding: 0;
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
}

/* Overlay area */
.overlay {
  position: fixed;
  top: 0;
  left: 0;
  bottom: 0px;
  width: 100%;
  height: 100%;
  background-color: white;
  z-index: 999;
  display: none;
  overflow-y: auto;
  box-sizing: border-box;
  pointer-events: none; /* Tidak menangkap klik, hanya sebagai background */
}

.overlay.show {
  display: block !important;
}

/* Enable page scroll when suggestions are shown - sesuai docs/facet.html */
body.show-suggestions {
  overflow-y: auto; /* Enable scroll pada halaman */
  position: static; /* Normal positioning untuk scroll */
  width: 100%;
  height: auto; /* Allow normal height */
}

/* Suggestions Container - Persis seperti docs/facet.html dengan scroll halaman */
.suggestions-container {
  background-color: white;
  width: 100%;
  max-width: 800px;
  margin: 0 auto;
  margin-top: 70px; /* Posisi sesuai facet.html */
  border-radius: 3px;
  overflow: visible; /* Tidak ada scroll di container */
  transition: all 0.3s ease;
  box-sizing: border-box;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  padding: 0;
  position: relative; /* Relative positioning untuk scroll halaman */
  z-index: 999; /* Di bawah header yang z-index 1000+ */
  pointer-events: auto; /* Pastikan container dapat menerima klik */
  height: auto; /* Auto height, scroll di halaman */
  min-height: calc(100vh - 80px); /* Minimal tinggi untuk overlay effect */
}

/* Keyword button container - sesuai docs/facet.html */
.keyword-button-container {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  padding: 15px;
  border-bottom: 1px solid #f2f2f2;
}

/* Keyword button styling - lebar menyesuaikan text dengan icon */
.keyword-button {
  display: inline-flex;
  align-items: center;
  background-color: white;
  border: 1px solid #e0e0e0;
  border-radius: 16px;
  padding: 6px 12px;
  font-size: 13px;
  color: #333;
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;
  width: auto; /* Auto width sesuai content */
  min-width: fit-content;
}

.keyword-button:hover {
  border-color: #ee4d2d;
  color: #ee4d2d;
  background-color: #fff9f8;
}

.keyword-button .suggestion-icon {
  margin-right: 6px;
  font-size: 12px;
  color: #999;
  display: flex;
  align-items: center;
}

.keyword-button .keyword-button-text {
  font-size: 13px;
  color: inherit;
}

/* Empty history message styling */
.empty-history-message {
  padding: 20px 15px;
  text-align: center;
  color: #999;
  font-size: 14px;
  border-bottom: 1px solid #f2f2f2;
}

.empty-history-message p {
  margin: 0;
}

/* Main keyword suggestions - SEBELUM klik "Lihat Lainnya" = bentuk tombol/tag */
.main-keyword-suggestions-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 10px;
  width: 100%;
  padding: 15px;
  box-sizing: border-box;
  border-bottom: 1px solid #f2f2f2;
}

.main-keyword-suggestion-tag {
  background-color: #f5f5f5;
  border-radius: 20px;
  padding: 8px 12px;
  font-size: 13px;
  color: #333;
  text-align: center;
  cursor: pointer;
  transition: all 0.2s ease;
  border: none;
  outline: none;
  display: block;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.main-keyword-suggestion-tag:hover {
  background-color: #ffe8e3;
  color: #ee4d2d;
}

/* Main keyword suggestions - SETELAH klik "Lihat Lainnya" = bentuk list */
.main-keyword-suggestions-list {
  padding: 0;
  border-bottom: 1px solid #f2f2f2;
}

.main-keyword-suggestions-list .suggestion-item {
  padding: 10px 15px;
  cursor: pointer;
  display: flex;
  align-items: center;
  transition: background-color 0.2s ease;
}

.main-keyword-suggestions-list .suggestion-item:hover {
  background-color: #f9f9f9;
}

/* Main see more container styling */
.main-see-more-container {
  padding: 10px 0;
  text-align: center;
  transition: all 0.2s ease;
  position: relative;
  z-index: 2;
  background-color: white;
  margin-bottom: 15px;
  display: block;
  border-bottom: 1px solid #f2f2f2;
}

.suggestion-item {
  padding: 12px 15px;
  cursor: pointer;
  display: flex;
  align-items: flex-start;
  border-bottom: 1px solid #f5f5f5;
  transition: background-color 0.2s ease;
}

.suggestion-item:hover {
  background-color: #f9f9f9;
}

.suggestion-item:last-child {
  border-bottom: 1px solid #f5f5f5;
}

.suggestion-icon {
  margin-right: 12px;
  width: 20px;
  display: inline-flex;
  justify-content: center;
  color: #999;
  font-size: 14px;
  flex-shrink: 0;
  align-self: flex-start;
  margin-top: 1px;
}

.suggestion-icon i {
  font-size: 14px;
  color: #999;
}

.suggestion-text {
  display: inline-block;
  line-height: 1.4;
  word-break: break-word;
  color: #333;
  font-size: 14px;
}

/* Hide main content when suggestions are shown */
body.show-suggestions main,
body.show-suggestions .container,
body.show-suggestions section {
  display: none !important;
  visibility: hidden !important;
}

/* Ensure suggestions containers are always visible */
body.show-suggestions .suggestions-container {
  display: block !important;
  visibility: visible !important;
}

/* Clear history styling - sesuai docs/facet.html */
.clear-history {
  text-align: center;
  padding: 10px;
  margin-bottom: 5px;
  cursor: pointer;
  color: #999;
  font-size: 13px;
  border-bottom: 1px solid #f2f2f2;
  transition: color 0.2s ease;
}

.clear-history:hover {
  color: #ee4d2d;
}

/* Extended suggestions styling - sesuai docs/facet.html */
.extended-suggestions {
  padding: 0;
  border-bottom: 1px solid #f2f2f2;
}

.extended-suggestions .suggestion-item {
  padding: 10px 15px;
  cursor: pointer;
  display: flex;
  align-items: center;
  transition: background-color 0.2s ease;
}

.extended-suggestions .suggestion-item:hover {
  background-color: #f9f9f9;
}

/* Trend pill styling - persis sesuai docs/facet.html */
.trend-pill {
  display: inline-flex;
  align-items: center;
  background-color: #fff4f2;
  color: #ee4d2d;
  font-size: 16px;
  font-weight: 500;
  padding: 6px 15px;
  border-radius: 15px;
  margin: 20px 0 20px 15px;
}

.trend-pill-badge {
  background-color: #ee4d2d;
  color: white;
  font-size: 10px;
  border-radius: 50%;
  width: 18px;
  height: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 8px;
  text-align: center;
}

/* Trending section styling */
.trending-section {
  padding: 15px 0;
  border-bottom: 1px solid #f2f2f2;
}

/* Keyword suggestion tags - SEBELUM klik "Lihat Lainnya" = bentuk tombol/tag */
.keyword-suggestions-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 10px;
  width: 100%;
  padding: 15px;
  box-sizing: border-box;
}

.keyword-suggestion-tag {
  background-color: #f5f5f5;
  border-radius: 20px;
  padding: 8px 12px;
  font-size: 13px;
  color: #333;
  text-align: center;
  cursor: pointer;
  transition: all 0.2s ease;
  border: none;
  outline: none;
  display: block;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.keyword-suggestion-tag:hover {
  background-color: #ffe8e3;
  color: #ee4d2d;
}

/* Keyword suggestions list - SETELAH klik "Lihat Lainnya" = bentuk list */
.keyword-suggestions-list {
  padding: 0;
}

.keyword-suggestions-list .suggestion-item {
  padding: 10px 15px;
  cursor: pointer;
  display: flex;
  align-items: center;
  transition: background-color 0.2s ease;
  border-bottom: none;
}

.keyword-suggestions-list .suggestion-item:hover {
  background-color: #f9f9f9;
}

.keyword-suggestions-list .suggestion-icon {
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  font-size: 14px;
  color: #999;
}

.keyword-suggestions-list .suggestion-text {
  font-size: 14px;
  color: #333;
}

/* Trending items styling */
.trending-item .suggestion-icon i {
  color: #ee4d2d !important;
}

/* Keyword suggestions popup styling - sesuai docs/facet.html */
.keyword-suggestions-popup {
  padding: 15px 0;
  border-top: 1px solid #f2f2f2;
  margin-top: 15px;
}

.keyword-suggestions-title {
  font-size: 14px;
  color: #666;
  margin-bottom: 15px;
  padding: 0 15px;
  font-weight: 500;
}

/* Trending title styling - sesuai docs/facet.html */
.trending-title {
  margin-top: 15px;
  color: #333;
  font-weight: 500;
  padding: 12px 15px 5px;
  font-size: 13px;
  display: flex;
  align-items: center;
}

.trending-title .trend-icon {
  margin-left: 5px;
  font-size: 13px;
  color: #ee4d2d;
}

/* Additional suggestions styling */
.additional-suggestions {
  max-height: none;
  overflow: visible;
  transition: max-height 0.3s ease;
  border-bottom: 0px solid #f2f2f2;
  background-color: white;
  position: relative;
  z-index: 1;
  margin-bottom: 15px;
  display: block;
}

.additional-suggestions.open {
  max-height: none;
  border-bottom: 1px solid #f2f2f2;
  padding-bottom: 20px;
}

.additional-suggestions .suggestion-item {
  padding: 10px 15px;
  cursor: pointer;
  display: flex;
  align-items: center;
}

.additional-suggestions .suggestion-item:hover {
  background-color: #f9f9f9;
}

.additional-suggestions .suggestion-icon {
  font-size: 18px !important;
  margin-bottom: 6px;
  color: #ee4d2d; /* Warna oranye untuk ikon trend */
}

/* See more button styling - persis seperti docs/facet.html */
.see-more-container {
  padding: 10px 0;
  text-align: center;
  transition: all 0.2s ease;
  position: relative;
  z-index: 2;
  background-color: white;
  margin-bottom: 15px;
  display: block;
  border-bottom: 1px solid #f2f2f2;
}

.see-more-btn {
  background-color: transparent;
  color: #ee4d2d;
  border: none;
  padding: 8px 15px;
  font-size: 14px;
  cursor: pointer;
  border-radius: 4px;
  font-weight: 500;
  text-align: center;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s ease;
  gap: 5px;
}

.see-more-btn:hover {
  background-color: #f9f9f9;
}

.see-more-btn i {
  color: #ee4d2d;
  font-size: 14px;
}

/* Keyword Predictions Container - sesuai docs/facet.html */
.keyword-predictions {
  position: absolute;
  top: 50px;
  left: 0;
  right: 0;
  background-color: white;
  border-radius: 0 0 8px 8px;
  box-shadow: 0 2px 5px rgba(0,0,0,0.1);
  z-index: 1000;
  max-width: 800px;
  margin: 10px auto;
  transform: translateY(10px);
  /* Hapus max-height dan overflow untuk menghilangkan scroll internal */
  height: auto;
  overflow: visible;
}

/* Hapus scrollbar styling karena tidak ada scroll internal */

/* Prediction item styling */
.prediction-item {
  padding: 10px 15px;
  cursor: pointer;
  border-bottom: 1px solid #f5f5f5;
  display: flex;
  align-items: flex-start;
  transition: background-color 0.2s ease;
}

.prediction-item:hover {
  background-color: #f9f9f9;
}

.prediction-item:last-child {
  border-bottom: none;
}

/* Prediction icon styling */
.prediction-icon {
  margin-right: 12px;
  width: 20px;
  display: inline-flex;
  justify-content: center;
  color: #999;
  flex-shrink: 0;
  align-self: flex-start;
  margin-top: 2px;
}

/* Matched prediction icon (orange color) */
.prediction-icon.matched {
  color: #ee4d2d;
}

/* Prediction text styling */
.prediction-text {
  font-size: 14px;
  color: #333;
  line-height: 1.4;
  word-break: break-word;
}

/* Highlighted text in predictions */
.prediction-text .highlighted {
  color: #ee4d2d;
  font-weight: bold;
}

/* Responsive styling for predictions - sesuai docs/facet.html */
@media (min-width: 768px) {
  .keyword-predictions {
    top: 50px;
    border-radius: 8px;
    margin-top: 25px;
    /* Pastikan tidak ada scroll internal di desktop */
    overflow: visible;
    height: auto;
  }
}

/* Pastikan body dapat scroll saat predictions muncul */
body.show-suggestions {
  overflow-y: auto !important;
  position: static !important;
  width: 100% !important;
  height: auto !important;
}

/* Search Results Container - sesuai docs/facet.html */
.search-results-container {
  position: absolute;
  top: 60px;
  background-color: white !important;
  left: 0;
  right: 0;
  background-color: white;
  border-radius: 0 0 8px 8px;
  box-shadow: 0 2px 5px rgba(0,0,0,0.1);
  z-index: 1000;
  max-width: calc(100% - 80px);
  margin: 10px 60px 10px auto;
  transform: translateY(10px);
  height: auto;
  overflow: visible;
  padding: 20px;
}

.search-results-header {
  margin-bottom: 20px;
  border-bottom: 1px solid #f0f0f0;
  padding-bottom: 15px;
}

.search-results-header h3 {
  margin: 0 0 5px 0;
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.results-count {
  font-size: 14px;
  color: #666;
}

/* Filter Tabs Styling - sesuai docs/facet.html */
.filter-tabs-container {
  margin: 20px 0;
  border-bottom: 1px solid #f0f0f0;
}

.filter-tabs {
  display: flex;
  gap: 0;
  overflow-x: auto;
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.filter-tabs::-webkit-scrollbar {
  display: none;
}

.filter-tab {
  background: none;
  border: none;
  padding: 12px 20px;
  font-size: 14px;
  font-weight: 500;
  color: #666;
  cursor: pointer;
  border-bottom: 2px solid transparent;
  transition: all 0.2s ease;
  white-space: nowrap;
  min-width: fit-content;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
}

.filter-tab:hover {
  color: #ff6b35;
  background-color: #fff5f2;
}

.filter-tab.active {
  color: #ff6b35;
  border-bottom-color: #ff6b35;
  font-weight: 600;
}

.filter-tab.active::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  right: 0;
  height: 2px;
  background-color: #ff6b35;
  border-radius: 1px 1px 0 0;
}

/* No Results Styling */
.no-results {
  text-align: center;
  padding: 40px 20px;
  color: #666;
}

.no-results-icon {
  font-size: 48px;
  color: #ddd;
  margin-bottom: 20px;
}

.no-results h4 {
  margin: 0 0 10px 0;
  font-size: 18px;
  color: #333;
}

.no-results p {
  margin: 0;
  font-size: 14px;
  color: #666;
}

/* Search Results Grid */
.search-results-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 15px;
  margin-top: 20px;
}

.search-result-card {
  border: 1px solid #f0f0f0; /* Restore individual card border */
  border-radius: 8px;
  padding: 15px;
  background-color: white; /* Restore white background for cards */
  transition: box-shadow 0.2s ease;
  position: relative;
  cursor: pointer;
}

.search-result-card:hover {
  box-shadow: 0 4px 12px rgba(0,0,0,0.1); /* Restore original hover effect */
}

.result-product-img {
  width: 100%;
  height: 200px;
  object-fit: cover;
  border-radius: 6px;
  margin-bottom: 12px;
}

.result-product-info {
  margin-bottom: 10px;
}

.result-product-name {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin-bottom: 8px;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.sellzio-mall-icon {
  background-color: #ee4d2d;
  color: white;
  font-size: 10px;
  padding: 2px 6px;
  border-radius: 3px;
  margin-right: 6px;
  font-weight: 600;
}

.result-product-rating-sold {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 6px;
}

.result-product-rating {
  display: flex;
  align-items: center;
  font-size: 12px;
  color: #666;
}

.result-product-rating i {
  color: #ffa500;
  margin-right: 4px;
}

.result-product-sold {
  font-size: 12px;
  color: #666;
}

.result-product-shipping {
  font-size: 12px;
  color: #666;
  margin-bottom: 8px;
}

.result-product-shipping i {
  margin-right: 4px;
  color: #00b14f;
}

.result-product-price-container {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
}

.result-product-price {
  font-size: 16px;
  font-weight: 600;
  color: #ee4d2d;
}

.result-product-price-original {
  font-size: 12px;
  color: #999;
  text-decoration: line-through;
}

.result-product-discount {
  font-size: 12px;
  color: #ee4d2d;
  background-color: #fff2f0;
  padding: 2px 6px;
  border-radius: 3px;
}

.result-cod-icon {
  position: absolute;
  top: 10px;
  right: 10px;
  background-color: #00b14f;
  color: white;
  font-size: 10px;
  padding: 4px 6px;
  border-radius: 3px;
  font-weight: 600;
}

.result-cod-icon i {
  margin-right: 3px;
}

/* Hide main content when search results are shown - sesuai docs/facet.html */
.hide-main-content .main-content {
  display: none !important;
}

.hide-main-content .header-content {
  display: none !important;
}

.hide-main-content .category-section {
  display: none !important;
}

.hide-main-content .hero-section {
  display: none !important;
}

.hide-main-content .product-section {
  display: none !important;
}

.hide-main-content .footer {
  display: none !important;
}

/* Ensure white background for search results - no black overlay */
.hide-main-content {
  background-color: white !important;
}

/* Remove any dark background when search results are shown */
body.hide-main-content {
  background-color: white !important;
}

/* Ensure search results container has full white background */
.search-results-container {
  background-color: white !important;
  min-height: 100vh !important;
}

/* Responsive untuk mobile - sesuai docs/facet.html */
@media (max-width: 767px) {
  .header {
    padding-top: 15px;
    padding-bottom: 20px;	/* Tambahkan padding atas lebih besar untuk mobile */
  }

  .header .header-icons {
    top: 50%;
    transform: translateY(-50%);
    padding-top: 10px;
    gap: 5px; /* Gap lebih kecil untuk mobile */
  }
  
  /* Media query untuk mobile tidak perlu mengatur ulang clear search icon karena sudah diatur di atas */
  
  .search-expanded .search-input {
    padding-right: 45px; /* Ruang untuk tombol clear search */
  }

  .search-results-grid {
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 12px;
  }

  .search-results-container {
    margin: 5px;
    padding: 15px;
  }
}

/* Product suggestions styling - sesuai docs/facet.html */
.product-suggestions {
  padding: 10px;
  width: 100%;
  box-sizing: border-box;
  margin-top: 10px;
}

.product-title {
  font-size: 14px;
  color: #666;
  margin-bottom: 15px;
  padding: 0 5px;
  display: block;
}

.product-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr); /* Mobile: 2 kolom */
  gap: 10px;
  width: 100%;
  box-sizing: border-box;
  border: 2px solid #eee;
  border-radius: 5px;
  margin-top: 30px;
  padding: 10px 10px 25px 10px;
  transform: translateY(0px);
}

/* Tablet: 3 kolom - sesuai docs/facet.html */
@media (min-width: 768px) and (max-width: 1023px) {
  .header, .search-expanded {
    padding: 15px;
    width: 100%;
    box-sizing: border-box;
  }

  .product-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: 12px;
  }
}

/* Desktop: 4 kolom - sesuai docs/facet.html */
@media (min-width: 1024px) {
  .desktop-view .header {
    padding: 15px;
    display: flex;
    justify-content: center;
    align-items: center;
    overflow: hidden;
  }

  .desktop-view .search-container {
    max-width: 800px;
    margin: 0 auto;
    width: 100%;
  }

  .desktop-view .search-expanded {
    display: flex;
    justify-content: center;
  }

  .desktop-view .search-expanded .search-container {
    width: 100%;
    max-width: 800px;
  }

  .product-grid {
    grid-template-columns: repeat(4, 1fr);
    gap: 15px;
  }
}

.simple-product-card {
  background-color: white;
  border-radius: 3px;
  transform: translateY(10px);
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0,0,0,0.15);
  cursor: pointer;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  text-align: center;
}

.simple-product-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0,0,0,0.2);
}

.product-img {
  width: 100%;
  height: 150px;
  object-fit: cover;
  display: block;
}

.simple-product-name {
  font-size: 13px;
  color: #333;
  padding: 10px 5px;
  text-align: center;
  height: auto;
  max-height: 40px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

/* Facet overlay untuk mobile */
.facet-overlay {
  position: fixed;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  background-color: rgba(0,0,0,0.5);
  z-index: 1050;
  display: none;
}

/* Panel facet untuk mobile/tablet */
.facet-panel {
  background-color: white;
  width: 100%;
  max-width: 500px;
  max-height: 80vh;
  border-radius: 10px 10px 0 0;
  overflow-y: auto;
  padding: 20px;
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  margin: 0 auto;
}

/* Panel facet untuk desktop */
.facet-panel-desktop {
  position: fixed;
  right: 0;
  top: 0;
  height: 100%;
  width: 350px;
  background-color: white;
  box-shadow: -2px 0 10px rgba(0,0,0,0.1);
  z-index: 1050;
  padding: 20px;
  overflow-y: auto;
  display: none;
}

.facet-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #f0f0f0;
}

.facet-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.facet-close {
  cursor: pointer;
  color: #666;
  font-size: 18px;
  padding: 5px;
}

.facet-close:hover {
  color: #ee4d2d;
}

.facet-content-wrapper {
  margin-bottom: 20px;
}

.facet-buttons {
  display: flex;
  gap: 10px;
  padding-top: 15px;
  border-top: 1px solid #f0f0f0;
}

.facet-button {
  flex: 1;
  padding: 12px;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  border: none;
  transition: all 0.2s ease;
}

.facet-button-reset {
  background-color: #f5f5f5;
  color: #666;
}

.facet-button-reset:hover {
  background-color: #e0e0e0;
}

.facet-button-apply {
  background-color: #ee4d2d;
  color: white;
}

.facet-button-apply:hover {
  background-color: #d63916;
}

/* Responsive untuk facet */
@media (min-width: 1025px) {
  .facet-panel-desktop {
    display: block;
  }
  .facet-overlay {
    display: none !important;
  }
}



/* Facet Section Styling - sesuai docs/facet.html */
.facet-section {
  margin-bottom: 20px;
  padding: 15px;
  background-color: white;
  border-bottom: 1px solid #f2f2f2;
}

.facet-section h3 {
  font-size: 16px;
  color: #333;
  margin-bottom: 15px;
  font-weight: 600;
  padding: 0;
}

.facet-section ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.facet-section li {
  padding: 6px 0;
  display: flex;
  align-items: center;
}

.facet-section input[type="checkbox"] {
  margin-right: 12px;
}

.facet-section label {
  font-size: 14px;
  color: #666;
  cursor: pointer;
  flex: 1;
}

.facet-section label:hover {
  color: #ee4d2d;
}

.facet-section input[type="checkbox"]:checked + label {
  color: #ee4d2d;
  font-weight: 500;
}

/* Orange checkbox styling */
.orange-checkbox {
  position: relative;
  cursor: pointer;
  appearance: none;
  -webkit-appearance: none;
  width: 18px;
  height: 18px;
  border: 1px solid #ccc;
  border-radius: 3px;
  outline: none;
  transition: all 0.2s ease;
  vertical-align: middle;
  margin-right: 12px;
}

.orange-checkbox:checked {
  background-color: #ee4d2d;
  border-color: #ee4d2d;
}

.orange-checkbox:checked::after {
  content: '';
  position: absolute;
  left: 5px;
  top: 3px;
  width: 5px;
  height: 9px;
  border: solid white;
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
}

/* Styling untuk label saat checkbox di-check */
.orange-checkbox:checked + label {
  color: #ee4d2d;
  font-weight: 500;
}

/* Active Filters Styling */
.active-filters {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-bottom: 15px;
}

.filter-tag {
  background-color: #f5f5f5;
  border-radius: 16px;
  padding: 5px 10px;
  font-size: 12px;
  color: #333;
  display: flex;
  align-items: center;
  cursor: pointer;
}

.filter-tag i {
  margin-left: 6px;
  color: #666;
  cursor: pointer;
  font-size: 11px;
  padding: 2px;
  border-radius: 50%;
  transition: all 0.2s ease;
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.filter-tag i:hover {
  color: #ee4d2d;
  background-color: rgba(238, 77, 45, 0.1);
}

/* Facet overlay untuk mobile */
.facet-overlay {
  position: fixed;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1050;
  display: none;
  justify-content: center;
  align-items: flex-end;
}

/* Panel facet untuk mobile/tablet */
.facet-panel {
  background-color: white;
  width: 100%;
  max-width: 500px;
  max-height: 80vh;
  border-radius: 10px 10px 0 0;
  display: flex;
  flex-direction: column;
  position: relative;
  overflow: hidden;
  margin: 0 auto;
  animation: slideUp 0.3s ease;
}

@keyframes slideUp {
  from { transform: translateY(100%); }
  to { transform: translateY(0); }
}

/* Panel facet untuk desktop */
.facet-panel-desktop {
  position: fixed;
  right: 0;
  top: 0;
  height: 100%;
  width: 350px;
  background-color: white;
  box-shadow: -2px 0 10px rgba(0,0,0,0.1);
  z-index: 1050;
  display: none;
  flex-direction: column;
  animation: slideIn 0.3s ease;
}

@keyframes slideIn {
  from { transform: translateX(100%); }
  to { transform: translateX(0); }
}

/* Animation for tablet panel sliding from right */
@keyframes slideInFromRight {
  from { transform: translateX(100%); }
  to { transform: translateX(0); }
}

.facet-header {
  position: sticky;
  top: 0;
  display: flex;
  background-color: white;
  z-index: 10;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  border-bottom: 1px solid #f2f2f2;
}

.facet-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.facet-close {
  cursor: pointer;
  color: #999;
  font-size: 18px;
  padding: 5px;
}

.facet-close:hover {
  color: #ee4d2d;
}

.facet-content-wrapper {
  flex: 1;
  overflow-y: auto;
  padding: 0 15px;
  max-height: calc(80vh - 120px);
  -webkit-overflow-scrolling: touch;
  overscroll-behavior: contain;
}

.facet-buttons {
  display: flex;
  gap: 10px;
  padding: 15px;
  border-top: 1px solid #f2f2f2;
  background-color: white;
}

.facet-button {
  flex: 1;
  padding: 12px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  text-align: center;
}

.facet-button-reset {
  background-color: #f5f5f5;
  color: #666;
}

.facet-button-reset:hover {
  background-color: #e8e8e8;
}

.facet-button-apply {
  background-color: #ee4d2d;
  color: white;
}

.facet-button-apply:hover {
  background-color: #d63916;
}

/* Tambahkan ruang kosong setelah konten facet terakhir */
.facet-section:last-child {
  margin-bottom: 30px;
  border-bottom: none;
}

/* Mencegah scroll pada body saat facet panel aktif */
body.facet-active {
  overflow: hidden;
}

/* Pastikan panel facet memiliki padding yang tepat */
.facet-panel, .facet-panel-desktop {
  overscroll-behavior: contain;
  padding: 0;
}

/* Desktop Sidebar Layout */
.search-results-layout {
  display: flex;
  gap: 0; /* Remove gap so sidebar sticks to product container */
  width: 100%;
  max-width: none; /* Full page width for desktop */
  margin: 0 20px 20px 20px; /* Add side margins instead of centering */
  padding: 0; /* Remove internal padding to make components truly stick together */
  background-color: white; /* Add background to create unified container look */
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1); /* Add subtle shadow for container effect */
  border: 1px solid #e5e5e5;
  overflow: visible; /* Ubah dari hidden ke visible agar konten facet bisa scroll */
}

/* Desktop Facet Sidebar */
.desktop-facet-sidebar {
  width: 280px;
  flex-shrink: 0;
  display: none; /* Hidden on mobile/tablet */
  margin-right: 0; /* Ensure no margin */
  padding-top: 20px; /* Add spacing from header */
}

.facet-sidebar-desktop {
  background-color: white; /* Same background as product area */
  border: none; /* Remove border since parent container has border */
  border-radius: 8px 0 0 8px; /* Round only left corners to match container */
  border-right: 1px solid #e5e5e5; /* Add separator line between sidebar and products */
  padding: 0; /* Remove padding, let child elements handle their own padding */
  height: auto; /* Auto height instead of 100% to prevent scroll */
  min-height: auto; /* Remove minimum height to prevent scroll */
  max-height: none; /* Remove max height restriction */
  overflow: visible; /* Remove scroll effect */
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
}

/* Desktop sidebar specific header styling */
.facet-sidebar-desktop .facet-header {
  background-color: white; /* Same background as product area */
  padding: 20px 20px 15px 20px;
  border-bottom: 1px solid #e5e5e5;
  border-radius: 8px 0 0 0; /* Round only top-left corner */
  margin-bottom: 0;
}

/* Filter icon styling for desktop sidebar */
.facet-sidebar-desktop .facet-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.facet-filter-icon {
  color: #666;
  flex-shrink: 0;
}

/* Desktop sidebar specific content styling */
.facet-sidebar-desktop .facet-content-wrapper {
  flex: 1;
  padding: 0 20px;
  background-color: white; /* Same background as product area */
  overflow: visible; /* Remove scroll effect */
  max-height: none; /* Remove height restriction */
}

/* Desktop sidebar specific content */
.facet-sidebar-desktop .facet-content {
  overflow: visible; /* Remove scroll effect */
  max-height: none; /* Remove height restriction */
}

/* Desktop sidebar specific buttons styling */
.facet-sidebar-desktop .facet-buttons {
  padding: 15px 20px 20px 20px;
  background-color: white; /* Same background as product area */
  border-radius: 0 0 0 8px; /* Round only bottom-left corner */
}

/* Search Results Content Area - Override global styles */
.search-results-layout .search-results-container {
  flex: 1;
  margin: 0 !important;
  padding: 20px 60px 20px 20px !important;
  background-color: transparent !important; /* Remove background to prevent double container effect */
  border: none !important; /* Remove border since parent container has border */
  border-radius: 0 !important; /* Remove border radius to prevent double container effect */
  min-height: 500px !important; /* Ensure minimum height to match sidebar */
  box-sizing: border-box !important;
  position: static !important; /* Override absolute positioning */
  top: auto !important;
  left: auto !important;
  right: 0 !important;
  width: calc(100% - 60px) !important;
  max-width: none !important; /* Hapus batasan max-width */
}

/* Adjust grid for sidebar layout */
.search-results-layout .search-results-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 15px;
  width: 100%;
  max-width: none;
}

/* Desktop responsive */
@media (min-width: 1024px) {
  .desktop-facet-sidebar {
    display: block;
    position: sticky;
    top: 80px; /* Memberikan jarak dari header */
    height: auto;
    z-index: 10;
  }
  
  /* Khusus desktop - container yang bisa di-scroll */
  .search-results-layout .search-results-container {
    padding-right: 80px !important;
    width: calc(100% - 80px) !important;
    max-height: calc(100vh - 130px); /* Tinggi maksimum untuk container produk */
    overflow-y: auto; /* Container produk yang bisa di-scroll */
  }
  
  .search-results-container {
    max-width: calc(100% - 100px);
    margin-right: 100px;
  }

  .search-results-layout .search-results-grid {
    grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
    gap: 20px;
  }
  
  .search-results-layout {
    min-height: calc(100vh - 130px); /* Tinggi minimum layout */
    align-items: flex-start; /* Untuk memastikan item mulai dari atas */
  }
  
  /* Panel facet tidak boleh scroll */
  .facet-sidebar-desktop .facet-content-wrapper {
    overflow: visible;
    max-height: none;
  }
}

/* Tablet responsive */
@media (min-width: 768px) and (max-width: 1023px) {
  .search-results-layout {
    flex-direction: column;
    margin: 0 !important;
    max-width: 100% !important;
    width: 100% !important;
    padding: 0 !important;
  }

  .desktop-facet-sidebar {
    display: none;
  }

  .search-results-layout .search-results-container {
    background-color: white !important; /* Restore background for tablet */
    border-radius: 0 !important;
    min-height: 300px !important; /* Adjust minimum height for tablet */
    max-width: 100% !important;
    width: 100% !important;
    margin: 0 !important;
    padding: 10px !important;
    border: none !important;
  }

  .search-results-container {
    max-width: 100% !important;
    margin: 0 !important;
    width: 100% !important;
    padding: 10px !important;
    right: 0 !important;
    left: 0 !important;
  }

  .search-results-layout .search-results-grid {
    grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
    gap: 15px;
  }

  /* Tablet specific facet panel - slide from right */
  .facet-overlay {
    justify-content: flex-end !important; /* Align panel to right */
    align-items: stretch !important; /* Full height */
  }

  .facet-panel {
    max-width: 400px !important; /* Fixed width for tablet */
    width: 400px !important; /* Fixed width instead of full width */
    height: 100vh !important; /* Full height */
    max-height: 100vh !important; /* Full height */
    margin: 0 !important; /* Remove margins */
    border-radius: 0 !important; /* Remove border radius */
    animation: slideInFromRight 0.3s ease !important; /* Slide from right animation */
    position: relative !important;
    right: 0 !important;
    box-shadow: -2px 0 10px rgba(0,0,0,0.1) !important; /* Add shadow like desktop */
  }

  /* Adjust content wrapper for tablet full height */
  .facet-panel .facet-content-wrapper {
    max-height: calc(100vh - 120px) !important; /* Adjust for full height */
  }
}

/* Not Found Container - sesuai docs/facet.html */
.not-found-container {
  text-align: center;
  padding: 30px 20px 40px;
  margin: 0;
  background-color: #fff;
  width: 100%;
  position: relative;
}

.not-found-icon {
  margin: 0 auto 20px;
  width: 120px;
  height: 120px;
  opacity: 0.5;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Search Document Icon - sesuai docs/facet.html */
.search-document-icon {
  position: relative;
  width: 80px;
  height: 100px;
}

.document-base {
  width: 60px;
  height: 80px;
  background-color: #ddd;
  border-radius: 4px;
  position: absolute;
  top: 10px;
  left: 10px;
}

.document-fold {
  width: 15px;
  height: 15px;
  background-color: #bbb;
  position: absolute;
  top: 10px;
  right: 10px;
  clip-path: polygon(0 0, 0 100%, 100% 100%);
}

.document-lines {
  position: absolute;
  top: 25px;
  left: 20px;
  width: 40px;
}

.document-lines::before,
.document-lines::after {
  content: '';
  display: block;
  width: 100%;
  height: 2px;
  background-color: #bbb;
  margin-bottom: 5px;
}

.magnifying-glass {
  width: 30px;
  height: 30px;
  border: 3px solid #999;
  border-radius: 50%;
  position: absolute;
  bottom: -5px;
  right: -5px;
}

.magnifying-glass::after {
  content: '';
  width: 3px;
  height: 12px;
  background-color: #999;
  position: absolute;
  bottom: -15px;
  right: -5px;
  border-radius: 10px;
  transform: rotate(45deg);
}

.not-found-title {
  font-size: 18px;
  font-weight: 500;
  color: #333;
  margin-bottom: 10px;
}

.not-found-message {
  font-size: 14px;
  color: #888;
  margin-bottom: 25px;
}

.not-found-button-container {
  display: flex;
  flex-direction: column;
  gap: 12px;
  max-width: 400px;
  margin: 0 auto;
}

.not-found-button {
  border-radius: 4px;
  padding: 12px 16px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  border: none;
  transition: all 0.2s ease;
}

.not-found-button.primary {
  background-color: #ee4d2d;
  color: white;
}

.not-found-button.primary:hover {
  background-color: #e04224;
}

.not-found-button.secondary {
  background-color: white;
  color: #ee4d2d;
  border: 1px solid #ee4d2d;
}

.not-found-button.secondary:hover {
  background-color: rgba(238, 77, 45, 0.05);
}

/* Mobile responsive untuk not found */
@media (max-width: 576px) {
  .not-found-container {
    padding: 20px 15px 30px;
    margin: 10px 0;
  }

  .not-found-button-container {
    max-width: 100%;
  }

  .not-found-message {
    max-width: 95%;
  }

  .not-found-icon {
    width: 100px;
    height: 100px;
  }

  .not-found-button {
    padding: 10px 12px;
    font-size: 13px;
  }
}

/* Mobile responsive */
@media (max-width: 767px) {
  .search-results-layout {
    flex-direction: column;
    margin: 0 !important;
    max-width: 100% !important;
    width: 100% !important;
    padding: 0 !important;
  }

  .desktop-facet-sidebar {
    display: none;
  }

  .search-results-layout .search-results-container {
    background-color: white !important; /* Restore background for mobile */
    border-radius: 0 !important;
    min-height: 300px !important;
    max-width: 100% !important;
    width: 100% !important;
    margin: 0 !important;
    padding: 8px !important;
    border: none !important;
  }

  .search-results-container {
    max-width: 100% !important;
    margin: 0 !important;
    width: 100% !important;
    padding: 8px !important;
    right: 0 !important;
    left: 0 !important;
  }

  .search-results-layout .search-results-grid {
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 10px;
  }

  /* Mobile specific facet panel - slide from bottom */
  .facet-overlay {
    justify-content: center; /* Center panel */
    align-items: flex-end; /* Align to bottom */
  }

  .facet-panel {
    width: 100%; /* Full width for mobile */
    max-width: 500px; /* Max width for mobile */
    height: auto; /* Auto height for mobile */
    max-height: 80vh; /* Max height for mobile */
    border-radius: 10px 10px 0 0; /* Rounded top corners */
    animation: slideUp 0.3s ease; /* Slide up animation for mobile */
    box-shadow: none; /* Remove shadow for mobile */
  }

  /* Mobile content wrapper */
  .facet-panel .facet-content-wrapper {
    max-height: calc(80vh - 120px); /* Adjust for mobile height */
  }
}

/* Media queries untuk responsive facet */
@media (min-width: 1025px) {
  .facet-panel-desktop {
    display: block;
  }
  .facet-overlay {
    display: none !important;
  }
}

/* Responsive */
@media (max-width: 480px) {
  .cart-icon, .chat-icon {
    font-size: 20px;
  }

  .cart-icon {
    -webkit-text-stroke: 0.8px white;
  }

  .chat-icon {
    -webkit-text-stroke: 1.2px white;
    margin-left: 15px;
  }

  .cart-badge, .chat-badge {
    width: 18px !important;
    height: 18px !important;
    min-width: 18px !important;
    font-size: 10px;
    border-radius: 50% !important; /* Pastikan lingkaran sempurna */
    top: -10px !important; /* Posisi lebih ke atas agar tidak terpotong */
    right: -6px !important; /* Posisi lebih ke kanan agar tidak terpotong */
    -webkit-text-stroke: 0.4px #ee4d2d; /* Garis lebih tipis pada layar kecil */
    line-height: 18px !important; /* Pastikan teks rata tengah vertikal */
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    box-sizing: border-box !important;
    padding: 0 !important;
  }

  .chat-icon .chat-dots .dot {
    width: 2px;
    height: 2px;
  }

  /* Adjust clear icon position for mobile in expanded mode */
  .search-expanded .clear-search-icon {
    right: 10px !important; /* Ensure consistent positioning on mobile */
  }
}
